import os
import json
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class SpeechGrpcConfig:
    """语音识别gRPC服务配置"""
    
    # 服务配置
    service_name: str = "speech_recognition"
    port: str = "50051"
    max_workers: int = 20
    
    # 模型配置
    model_dir: str = "./SenseVoice_model"
    device: str = "cuda:0"
    
    # 音频处理配置
    default_sample_rate: int = 16000
    supported_formats: list = None
    max_audio_size: int = 50 * 1024 * 1024  # 50MB
    
    # 日志配置
    log_dir: str = "./logs"
    log_level: str = "INFO"
    log_days_to_keep: int = 7
    
    # gRPC配置
    max_receive_message_length: int = 1024 * 1024 * 50  # 50MB
    max_send_message_length: int = 1024 * 1024 * 50     # 50MB
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.wav', '.mp3', '.flac', '.m4a']
        
        # 从环境变量获取设备配置
        self.device = os.getenv("SENSEVOICE_DEVICE", self.device)
        
        # 从环境变量获取端口配置
        self.port = os.getenv("SPEECH_GRPC_PORT", self.port)
    
    @classmethod
    def from_json(cls, config_path: str) -> 'SpeechGrpcConfig':
        """从JSON文件加载配置"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            return cls(**config_dict)
        return cls()
    
    def to_json(self, config_path: str):
        """保存配置到JSON文件"""
        config_dict = {
            'service_name': self.service_name,
            'port': self.port,
            'max_workers': self.max_workers,
            'model_dir': self.model_dir,
            'device': self.device,
            'default_sample_rate': self.default_sample_rate,
            'supported_formats': self.supported_formats,
            'max_audio_size': self.max_audio_size,
            'log_dir': self.log_dir,
            'log_level': self.log_level,
            'log_days_to_keep': self.log_days_to_keep,
            'max_receive_message_length': self.max_receive_message_length,
            'max_send_message_length': self.max_send_message_length
        }
        
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=4)


# 全局配置实例
config = SpeechGrpcConfig.from_json('./speech_grpc_config.json')
