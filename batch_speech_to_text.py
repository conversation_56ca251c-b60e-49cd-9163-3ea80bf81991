#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量语音转文字工具
使用SenseVoice模型将语音文件批量转换为文字，并保存到CSV文件中
"""

import os
import csv
import glob
import sys
import time

def batch_speech_to_text(input_dir, output_csv, model_dir="./SenseVoice_model"):
    """
    批量将语音文件转换为文字并保存到CSV文件
    
    Args:
        input_dir (str): 包含语音文件的目录路径
        output_csv (str): 输出CSV文件路径
        model_dir (str): 模型目录路径
    """
    
    try:
        from funasr import AutoModel
        from funasr.utils.postprocess_utils import rich_transcription_postprocess
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装funasr库: pip install funasr")
        return False
    
    # 初始化模型
    # 录音机
    print("正在加载SenseVoice模型...")
    try:
        model = AutoModel(
            model=model_dir,
            trust_remote_code=True,
            remote_code="./model.py",
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device="cuda:0",
        )
        print("✓ 模型加载完成！")
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False
    
    # 支持的音频格式
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.m4a', '*.aac', '*.ogg']
    
    # 获取所有音频文件
    audio_files = []
    print(f"正在扫描目录: {input_dir}")
    
    for ext in audio_extensions:
        # 当前目录
        files = glob.glob(os.path.join(input_dir, ext))
        audio_files.extend(files)
        # 子目录（递归）
        files = glob.glob(os.path.join(input_dir, '**', ext), recursive=True)
        audio_files.extend(files)
    
    # 去重
    audio_files = list(set(audio_files))
    audio_files.sort()
    
    if not audio_files:
        print(f"✗ 在目录 {input_dir} 中未找到音频文件")
        print(f"支持的格式: {', '.join([ext.replace('*', '') for ext in audio_extensions])}")
        return False
    
    print(f"✓ 找到 {len(audio_files)} 个音频文件")
    
    # 准备结果列表
    results = []
    success_count = 0
    error_count = 0
    
    # 批量处理音频文件
    print("\n开始批量处理...")
    for i, audio_file in enumerate(audio_files, 1):
        try:
            print(f"[{i}/{len(audio_files)}] 处理: {os.path.basename(audio_file)}")
            
            # 获取绝对路径
            abs_path = os.path.abspath(audio_file)
            
            # 语音转文字
            start_time = time.time()
            res = model.generate(
                input=audio_file,
                cache={},
                language="auto",  # "zh", "en", "yue", "ja", "ko", "nospeech"
                use_itn=True,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=15,
            )
            process_time = time.time() - start_time
            
            # 后处理文本
            if res and len(res) > 0 and "text" in res[0]:
                text = rich_transcription_postprocess(res[0]["text"])
                # 清理文本中的换行符和多余空格
                text = text.replace('\n', ' ').replace('\r', ' ').strip()
                text = ' '.join(text.split())  # 合并多个空格为一个
            else:
                text = ""
            
            results.append([abs_path, text])
            success_count += 1
            
            # 显示结果预览
            preview = text[:50] + "..." if len(text) > 50 else text
            print(f"  ✓ 完成 ({process_time:.2f}s): {preview}")
            
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            print(f"  ✗ {error_msg}")
            results.append([os.path.abspath(audio_file), error_msg])
            error_count += 1
    
    # 保存到CSV文件
    print(f"\n正在保存结果到: {output_csv}")
    try:
        with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            # 写入表头（参考speech_asr_aishell_hotwords_testsets.csv格式）
            writer.writerow(['Audio:FILE', 'Text:LABEL'])
            # 写入数据
            writer.writerows(results)
        
        print(f"✓ 结果已保存到: {output_csv}")
        print(f"\n=== 处理统计 ===")
        print(f"总文件数: {len(audio_files)}")
        print(f"成功处理: {success_count}")
        print(f"处理失败: {error_count}")
        return True
        
    except Exception as e:
        print(f"✗ 保存CSV文件失败: {e}")
        return False

def main():
    """主函数"""
    print("=== SenseVoice 批量语音转文字工具 ===")
    print("此工具将批量处理语音文件并生成CSV格式的转录结果")
    print()
    
    # 获取输入目录
    if len(sys.argv) > 1:
        input_dir = sys.argv[1]
    else:
        input_dir = input("请输入语音文件目录路径 (默认: ./voice_test): ").strip()
        if not input_dir:
            input_dir = "./voice_test"
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"✗ 目录不存在: {input_dir}")
        return
    
    # 获取输出文件路径
    if len(sys.argv) > 2:
        output_csv = sys.argv[2]
    else:
        output_csv = input("请输入输出CSV文件路径 (默认: ./speech_results.csv): ").strip()
        if not output_csv:
            output_csv = "./speech_results.csv"
    
    # 获取模型目录
    if len(sys.argv) > 3:
        model_dir = sys.argv[3]
    else:
        model_dir = "./SenseVoice_model"
    
    print(f"\n配置信息:")
    print(f"  输入目录: {os.path.abspath(input_dir)}")
    print(f"  输出文件: {os.path.abspath(output_csv)}")
    print(f"  模型目录: {os.path.abspath(model_dir)}")
    print()
    
    # 确认开始处理
    if len(sys.argv) <= 1:  # 交互模式
        confirm = input("确认开始处理? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("已取消处理")
            return
    
    # 开始批量处理
    start_time = time.time()
    success = batch_speech_to_text(input_dir, output_csv, model_dir)
    total_time = time.time() - start_time
    
    if success:
        print(f"\n🎉 批量处理完成！总耗时: {total_time:.2f}秒")
        print(f"结果文件: {os.path.abspath(output_csv)}")
    else:
        print(f"\n❌ 批量处理失败！")

if __name__ == "__main__":
    main()
