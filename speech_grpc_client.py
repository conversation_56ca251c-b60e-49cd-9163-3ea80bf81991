import grpc
import time
import uuid
from pathlib import Path
from typing import List, Optional

# 导入生成的gRPC代码
import speech_recognition_pb2
import speech_recognition_pb2_grpc


class SpeechRecognitionClient:
    """语音识别gRPC客户端"""
    
    def __init__(self, server_address: str = "localhost:50051"):
        """
        初始化客户端
        
        Args:
            server_address: gRPC服务器地址
        """
        self.server_address = server_address
        self.channel = None
        self.stub = None
        self._connect()
    
    def _connect(self):
        """连接到gRPC服务器"""
        try:
            # 创建gRPC通道
            self.channel = grpc.insecure_channel(
                self.server_address,
                options=[
                    ('grpc.max_receive_message_length', 1024 * 1024 * 50),
                    ('grpc.max_send_message_length', 1024 * 1024 * 50),
                ]
            )
            
            # 创建存根
            self.stub = speech_recognition_pb2_grpc.SpeechRecognitionServiceStub(self.channel)
            
            print(f"已连接到gRPC服务器: {self.server_address}")
            
        except Exception as e:
            print(f"连接gRPC服务器失败: {e}")
            raise e
    
    def recognize_speech(self, 
                        audio_files: List[str],
                        language: str = "auto",
                        use_itn: bool = False,
                        ban_emo_unk: bool = False,
                        task_id: Optional[str] = None) -> dict:
        """
        执行语音识别
        
        Args:
            audio_files: 音频文件路径列表
            language: 语言类型
            use_itn: 是否使用逆文本标准化
            ban_emo_unk: 是否禁用情感和未知标记
            task_id: 任务ID，如果不提供则自动生成
            
        Returns:
            识别结果字典
        """
        try:
            # 生成任务ID
            if not task_id:
                task_id = str(uuid.uuid4())
            
            # 读取音频文件
            audio_bytes_list = []
            audio_keys = []
            
            for audio_file in audio_files:
                audio_path = Path(audio_file)
                if not audio_path.exists():
                    raise FileNotFoundError(f"音频文件不存在: {audio_file}")
                
                with open(audio_path, 'rb') as f:
                    audio_bytes = f.read()
                    audio_bytes_list.append(audio_bytes)
                    audio_keys.append(audio_path.name)
            
            print(f"准备识别 {len(audio_files)} 个音频文件...")
            
            # 构建请求
            request = speech_recognition_pb2.SpeechRecognitionRequest(
                taskId=task_id,
                audioFiles=audio_bytes_list,
                audioKeys=audio_keys,
                language=language,
                useItn=use_itn,
                banEmoUnk=ban_emo_unk,
                sampleRate=16000
            )
            
            # 发送请求
            start_time = time.time()
            response = self.stub.RecognizeSpeech(request)
            request_time = time.time() - start_time
            
            # 处理响应
            result = {
                "task_id": response.taskId,
                "code": response.code,
                "message": response.message,
                "processing_time": response.processingTime,
                "request_time": request_time,
                "results": []
            }
            
            for pb_result in response.results:
                result["results"].append({
                    "key": pb_result.key,
                    "raw_text": pb_result.rawText,
                    "clean_text": pb_result.cleanText,
                    "text": pb_result.text,
                    "confidence": pb_result.confidence,
                    "duration": pb_result.duration,
                    "language": pb_result.language
                })
            
            return result
            
        except Exception as e:
            print(f"语音识别请求失败: {e}")
            raise e
    
    def health_check(self) -> dict:
        """健康检查"""
        try:
            request = speech_recognition_pb2.HealthCheckRequest(
                service="speech_recognition"
            )
            
            response = self.stub.HealthCheck(request)
            
            return {
                "code": response.code,
                "message": response.message,
                "status": response.status
            }
            
        except Exception as e:
            print(f"健康检查失败: {e}")
            return {
                "code": 500,
                "message": f"健康检查异常: {str(e)}",
                "status": "ERROR"
            }
    
    def close(self):
        """关闭连接"""
        if self.channel:
            self.channel.close()
            print("gRPC连接已关闭")


def main():
    """测试主函数"""
    # 创建客户端
    client = SpeechRecognitionClient("localhost:50051")
    
    try:
        # 健康检查
        print("=" * 50)
        print("执行健康检查...")
        health_result = client.health_check()
        print(f"健康检查结果: {health_result}")
        
        # 测试语音识别
        print("=" * 50)
        print("测试语音识别...")
        
        # 使用示例音频文件
        test_audio_files = [
            "./voice_test/test.wav",
            # 可以添加更多测试文件
        ]
        
        # 检查测试文件是否存在
        existing_files = []
        for file_path in test_audio_files:
            if Path(file_path).exists():
                existing_files.append(file_path)
            else:
                print(f"测试文件不存在，跳过: {file_path}")
        
        if not existing_files:
            print("没有找到测试音频文件，请确保voice_test目录下有音频文件")
            return
        
        # 执行识别
        result = client.recognize_speech(
            audio_files=existing_files,
            language="zh",
            use_itn=False,
            ban_emo_unk=False
        )
        
        # 打印结果
        print(f"任务ID: {result['task_id']}")
        print(f"状态码: {result['code']}")
        print(f"消息: {result['message']}")
        print(f"服务器处理时间: {result['processing_time']:.2f}秒")
        print(f"请求总时间: {result['request_time']:.2f}秒")
        print(f"识别结果数量: {len(result['results'])}")
        
        for i, res in enumerate(result['results']):
            print(f"\n结果 {i+1}:")
            print(f"  文件: {res['key']}")
            print(f"  时长: {res['duration']:.2f}秒")
            print(f"  语言: {res['language']}")
            print(f"  置信度: {res['confidence']:.3f}")
            print(f"  原始文本: {res['raw_text']}")
            print(f"  清理文本: {res['clean_text']}")
            print(f"  处理文本: {res['text']}")
        
    except Exception as e:
        print(f"测试失败: {e}")
    
    finally:
        # 关闭连接
        client.close()


if __name__ == "__main__":
    main()
