# 首先导入警告抑制模块
import suppress_warnings

import os
import re
import time
import logging
import tempfile
import warnings
from typing import List, Dict, Any, Tuple
from io import BytesIO
import torchaudio
import torch
import numpy as np
from pydub import AudioSegment
from model import SenseVoiceSmall
from funasr.utils.postprocess_utils import rich_transcription_postprocess


class SpeechInference:
    """语音识别推理类"""

    def __init__(self, model_dir: str = "./SenseVoice_model", device: str = "cuda:0"):
        """
        初始化语音识别推理器

        Args:
            model_dir: 模型目录路径
            device: 推理设备
        """
        self.model_dir = model_dir
        self.device = device
        self.model = None
        self.kwargs = None
        self.regex = r"<\|.*\|>"
        self.logger = logging.getLogger("speech_recognition")

        # 支持的语言
        self.supported_languages = ["auto", "zh", "en", "yue", "ja", "ko", "nospeech"]

        self._load_model()

    def _load_model(self):
        """加载语音识别模型"""
        try:
            self.logger.info(f"开始加载语音识别模型，模型路径: {self.model_dir}")
            self.logger.info(f"使用设备: {self.device}")

            start_time = time.time()
            self.model, self.kwargs = SenseVoiceSmall.from_pretrained(
                model=self.model_dir,
                device=self.device
            )
            self.model.eval()

            load_time = time.time() - start_time
            self.logger.info(f"模型加载成功，耗时: {load_time:.2f}秒")

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise e

    def _validate_language(self, language: str) -> str:
        """验证并标准化语言参数"""
        if not language or language == "":
            return "auto"

        if language not in self.supported_languages:
            self.logger.warning(f"不支持的语言: {language}，使用默认语言: auto")
            return "auto"

        return language

    def _detect_audio_format(self, audio_bytes: bytes) -> str:
        """
        检测音频格式

        Args:
            audio_bytes: 音频文件的字节数据

        Returns:
            音频格式 ('wav', 'mp3', 'flac', 等)
        """
        # 检查文件头来判断格式
        if audio_bytes.startswith(b'RIFF') and b'WAVE' in audio_bytes[:12]:
            return 'wav'
        elif audio_bytes.startswith(b'ID3') or audio_bytes.startswith(b'\xff\xfb') or audio_bytes.startswith(b'\xff\xf3') or audio_bytes.startswith(b'\xff\xf2'):
            return 'mp3'
        elif audio_bytes.startswith(b'fLaC'):
            return 'flac'
        elif audio_bytes.startswith(b'OggS'):
            return 'ogg'
        else:
            # 默认尝试wav格式
            return 'wav'

    def _load_audio_from_bytes(self, audio_bytes: bytes) -> Tuple[torch.Tensor, int]:
        """
        从字节数据加载音频，支持多种格式

        Args:
            audio_bytes: 音频文件的字节数据

        Returns:
            音频张量和采样率
        """
        try:
            # 检测音频格式
            audio_format = self._detect_audio_format(audio_bytes)
            self.logger.debug(f"检测到音频格式: {audio_format}")

            # 首先尝试使用torchaudio直接加载
            try:
                file_io = BytesIO(audio_bytes)
                data_or_path_or_list, audio_fs = torchaudio.load(file_io)

                # 转换为单声道
                if data_or_path_or_list.shape[0] > 1:
                    data_or_path_or_list = data_or_path_or_list.mean(0)
                else:
                    data_or_path_or_list = data_or_path_or_list.squeeze(0)

                file_io.close()
                self.logger.debug(f"使用torchaudio成功加载音频，采样率: {audio_fs}")
                return data_or_path_or_list, audio_fs

            except Exception as torchaudio_error:
                self.logger.debug(f"torchaudio加载失败: {torchaudio_error}，尝试使用其他方法")

                # 如果torchaudio失败，使用pydub + librosa的方法
                return self._load_audio_with_pydub_librosa(audio_bytes, audio_format)

        except Exception as e:
            self.logger.error(f"音频加载失败: {e}")
            raise e

    def _load_audio_with_pydub_librosa(self, audio_bytes: bytes, audio_format: str) -> Tuple[torch.Tensor, int]:
        """
        使用pydub和librosa加载音频

        Args:
            audio_bytes: 音频文件的字节数据
            audio_format: 音频格式

        Returns:
            音频张量和采样率
        """
        try:
            # 使用pydub加载音频，抑制警告
            file_io = BytesIO(audio_bytes)

            # 临时抑制所有警告
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")

                if audio_format == 'mp3':
                    audio_segment = AudioSegment.from_mp3(file_io)
                elif audio_format == 'wav':
                    audio_segment = AudioSegment.from_wav(file_io)
                elif audio_format == 'flac':
                    audio_segment = AudioSegment.from_file(file_io, format='flac')
                elif audio_format == 'ogg':
                    audio_segment = AudioSegment.from_ogg(file_io)
                else:
                    # 尝试自动检测格式
                    audio_segment = AudioSegment.from_file(file_io)

            # 转换为单声道
            if audio_segment.channels > 1:
                audio_segment = audio_segment.set_channels(1)

            # 获取采样率
            sample_rate = audio_segment.frame_rate

            # 转换为numpy数组
            audio_data = np.array(audio_segment.get_array_of_samples(), dtype=np.float32)

            # 标准化到[-1, 1]范围
            if audio_segment.sample_width == 2:  # 16-bit
                audio_data = audio_data / 32768.0
            elif audio_segment.sample_width == 4:  # 32-bit
                audio_data = audio_data / 2147483648.0
            elif audio_segment.sample_width == 1:  # 8-bit
                audio_data = (audio_data - 128) / 128.0

            # 转换为torch张量
            audio_tensor = torch.from_numpy(audio_data)

            file_io.close()

            self.logger.debug(f"使用pydub+librosa成功加载音频，采样率: {sample_rate}")
            return audio_tensor, sample_rate

        except Exception as e:
            self.logger.error(f"pydub+librosa加载音频失败: {e}")
            raise e

    def _calculate_audio_duration(self, audio_tensor: torch.Tensor, sample_rate: int) -> float:
        """计算音频时长"""
        return len(audio_tensor) / sample_rate

    def inference(self,
                  audio_files: List[bytes],
                  audio_keys: List[str] = None,
                  language: str = "auto",
                  use_itn: bool = False,
                  ban_emo_unk: bool = False) -> List[Dict[str, Any]]:
        """
        执行语音识别推理

        Args:
            audio_files: 音频文件字节数据列表
            audio_keys: 音频文件名称列表
            language: 语言类型
            use_itn: 是否使用逆文本标准化
            ban_emo_unk: 是否禁用情感和未知标记

        Returns:
            识别结果列表
        """
        try:
            start_time = time.time()

            # 验证输入
            if not audio_files:
                self.logger.warning("音频文件列表为空")
                return []

            # 处理音频键名
            if not audio_keys or len(audio_keys) != len(audio_files):
                audio_keys = [f"audio_{i}" for i in range(len(audio_files))]

            # 验证语言参数
            language = self._validate_language(language)

            # 加载音频数据
            audios = []
            audio_durations = []
            audio_fs = 0

            for i, audio_bytes in enumerate(audio_files):
                try:
                    audio_tensor, fs = self._load_audio_from_bytes(audio_bytes)
                    audios.append(audio_tensor)
                    audio_durations.append(self._calculate_audio_duration(audio_tensor, fs))

                    if audio_fs == 0:
                        audio_fs = fs
                    elif audio_fs != fs:
                        self.logger.warning(f"音频 {audio_keys[i]} 采样率不一致: {fs} vs {audio_fs}")

                except Exception as e:
                    self.logger.error(f"处理音频 {audio_keys[i]} 失败: {e}")
                    continue

            if not audios:
                self.logger.error("没有成功加载的音频文件")
                return []

            # 执行推理
            self.logger.info(f"开始语音识别推理，音频数量: {len(audios)}, 语言: {language}")

            inference_start = time.time()
            res = self.model.inference(
                data_in=audios,
                language=language,
                use_itn=use_itn,
                ban_emo_unk=ban_emo_unk,
                key=audio_keys,
                fs=audio_fs,
                **self.kwargs,
            )
            inference_time = time.time() - inference_start

            if len(res) == 0:
                self.logger.warning("模型推理返回空结果")
                return []

            # 处理结果
            results = []
            for i, result_item in enumerate(res[0]):
                try:
                    # 原始文本
                    raw_text = result_item.get("text", "")

                    # 清理文本（去除特殊标记）
                    clean_text = re.sub(self.regex, "", raw_text, 0, re.MULTILINE)

                    # 后处理文本
                    processed_text = rich_transcription_postprocess(raw_text)

                    # 构建结果
                    result = {
                        "key": result_item.get("key", audio_keys[i] if i < len(audio_keys) else f"audio_{i}"),
                        "raw_text": raw_text,
                        "clean_text": clean_text,
                        "text": processed_text,
                        "confidence": result_item.get("confidence", 0.0),
                        "duration": audio_durations[i] if i < len(audio_durations) else 0.0,
                        "language": language
                    }
                    results.append(result)

                except Exception as e:
                    self.logger.error(f"处理结果 {i} 失败: {e}")
                    continue

            total_time = time.time() - start_time
            self.logger.info(f"语音识别完成，总耗时: {total_time:.2f}秒，推理耗时: {inference_time:.2f}秒")

            return results

        except Exception as e:
            self.logger.error(f"语音识别推理失败: {e}")
            raise e

    def health_check(self) -> bool:
        """健康检查"""
        try:
            return self.model is not None and torch.cuda.is_available()
        except Exception:
            return False
