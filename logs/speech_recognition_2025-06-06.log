[2025-06-06 15:02:52][MainThread][INFO]: ==================================================
[2025-06-06 15:02:52][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:02:52][MainThread][INFO]: ==================================================
[2025-06-06 15:02:52][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:02:52][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:02:52][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:02:52][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:02:56][MainThread][INFO]: 模型加载成功，耗时: 4.83秒
[2025-06-06 15:02:56][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:02:56][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:02:56][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:02:56][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:02:56][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:02:56][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:02:56][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:02:56][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c2e469e8-b792-459d-be1f-daa6a64deda9
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 任务 c2e469e8-b792-459d-be1f-daa6a64deda9: 音频数量=1, 语言=zh
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.13秒，推理耗时: 0.12秒
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 任务 c2e469e8-b792-459d-be1f-daa6a64deda9 完成，处理时间: 0.13秒，结果数量: 1
[2025-06-06 15:04:05][MainThread][INFO]: ==================================================
[2025-06-06 15:04:05][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:04:05][MainThread][INFO]: ==================================================
[2025-06-06 15:04:05][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:04:05][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:04:05][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:04:05][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:04:10][MainThread][INFO]: 模型加载成功，耗时: 4.95秒
[2025-06-06 15:04:10][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:04:10][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:04:10][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:04:10][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:04:10][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:04:10][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:04:10][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:04:10][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: b5e5ab37-8377-4795-97b7-5ba53befeb6d
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 任务 b5e5ab37-8377-4795-97b7-5ba53befeb6d: 音频数量=1, 语言=zh
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.12秒，推理耗时: 0.11秒
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 任务 b5e5ab37-8377-4795-97b7-5ba53befeb6d 完成，处理时间: 0.12秒，结果数量: 1
[2025-06-06 15:09:40][MainThread][INFO]: ==================================================
[2025-06-06 15:09:40][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:09:40][MainThread][INFO]: ==================================================
[2025-06-06 15:09:40][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:09:40][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:09:40][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:09:40][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:09:45][MainThread][INFO]: 模型加载成功，耗时: 5.04秒
[2025-06-06 15:09:45][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:09:45][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:09:45][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:09:45][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:09:45][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:09:45][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:09:45][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:09:45][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 任务 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6: 音频数量=1, 语言=zh
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.10秒，推理耗时: 0.10秒
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 任务 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 15:12:01][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:12:01][MainThread][INFO]: 服务已停止
[2025-06-06 15:15:08][MainThread][INFO]: ==================================================
[2025-06-06 15:15:08][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:15:08][MainThread][INFO]: ==================================================
[2025-06-06 15:15:08][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:15:08][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:15:08][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:15:08][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:15:13][MainThread][INFO]: 模型加载成功，耗时: 5.04秒
[2025-06-06 15:15:13][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:15:13][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:15:13][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:15:13][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:15:13][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:15:13][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:15:13][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:15:13][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ad645245-383c-45c3-a900-ec1ad11ca72b
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 任务 ad645245-383c-45c3-a900-ec1ad11ca72b: 音频数量=1, 语言=zh
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.10秒，推理耗时: 0.10秒
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 任务 ad645245-383c-45c3-a900-ec1ad11ca72b 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 589d505d-c1da-48db-bd5f-6d083f80c839
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 任务 589d505d-c1da-48db-bd5f-6d083f80c839: 音频数量=1, 语言=zh
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 任务 589d505d-c1da-48db-bd5f-6d083f80c839 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 任务 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe: 音频数量=1, 语言=zh
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 任务 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 596b12bb-d691-42c6-a6e0-83b18cb50b32
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 任务 596b12bb-d691-42c6-a6e0-83b18cb50b32: 音频数量=1, 语言=zh
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.30秒
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 任务 596b12bb-d691-42c6-a6e0-83b18cb50b32 完成，处理时间: 0.32秒，结果数量: 1
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c3786c44-e686-4630-9178-18ff1203f827
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 任务 c3786c44-e686-4630-9178-18ff1203f827: 音频数量=1, 语言=zh
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 处理音频 test.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 任务 c3786c44-e686-4630-9178-18ff1203f827 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 2bbef460-e131-4438-b64e-e1d92637b51d
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 任务 2bbef460-e131-4438-b64e-e1d92637b51d: 音频数量=1, 语言=zh
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 处理音频 test.wav 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 任务 2bbef460-e131-4438-b64e-e1d92637b51d 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1391a843-1f81-49b5-b21f-6b519e8afe16
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][INFO]: 任务 1391a843-1f81-49b5-b21f-6b519e8afe16: 音频数量=1, 语言=zh
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][INFO]: 任务 1391a843-1f81-49b5-b21f-6b519e8afe16 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:26:12][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:26:12][MainThread][INFO]: 服务已停止
[2025-06-06 15:26:41][MainThread][INFO]: ==================================================
[2025-06-06 15:26:41][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:26:41][MainThread][INFO]: ==================================================
[2025-06-06 15:26:41][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:26:41][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:26:41][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:26:41][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:26:46][MainThread][INFO]: 模型加载成功，耗时: 5.27秒
[2025-06-06 15:26:46][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:26:46][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:26:46][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:26:46][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:26:46][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:26:46][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:26:46][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:26:46][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:26:50][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0b200336-09cf-42fc-b7d5-9dfa06db8d5a
[2025-06-06 15:26:50][ThreadPoolExecutor-0_0][INFO]: 任务 0b200336-09cf-42fc-b7d5-9dfa06db8d5a: 音频数量=1, 语言=zh
[2025-06-06 15:26:53][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:26:53][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.39秒，推理耗时: 0.14秒
[2025-06-06 15:26:53][ThreadPoolExecutor-0_0][INFO]: 任务 0b200336-09cf-42fc-b7d5-9dfa06db8d5a 完成，处理时间: 3.39秒，结果数量: 1
[2025-06-06 15:27:10][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: d9006239-7f04-4e18-b2e4-5e9cb564dae9
[2025-06-06 15:27:10][ThreadPoolExecutor-0_0][INFO]: 任务 d9006239-7f04-4e18-b2e4-5e9cb564dae9: 音频数量=1, 语言=zh
[2025-06-06 15:27:13][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:27:13][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.30秒，推理耗时: 0.11秒
[2025-06-06 15:27:13][ThreadPoolExecutor-0_0][INFO]: 任务 d9006239-7f04-4e18-b2e4-5e9cb564dae9 完成，处理时间: 3.30秒，结果数量: 1
[2025-06-06 15:28:03][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ea4583ee-6388-4730-83ab-e897966b1083
[2025-06-06 15:28:03][ThreadPoolExecutor-0_0][INFO]: 任务 ea4583ee-6388-4730-83ab-e897966b1083: 音频数量=1, 语言=zh
[2025-06-06 15:28:03][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:28:04][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.10秒
[2025-06-06 15:28:04][ThreadPoolExecutor-0_0][INFO]: 任务 ea4583ee-6388-4730-83ab-e897966b1083 完成，处理时间: 0.31秒，结果数量: 1
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 011cda53-5fa3-4920-ad4b-fe077264453c
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 任务 011cda53-5fa3-4920-ad4b-fe077264453c: 音频数量=2, 语言=zh
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.32秒，推理耗时: 0.13秒
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 任务 011cda53-5fa3-4920-ad4b-fe077264453c 完成，处理时间: 0.32秒，结果数量: 2
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f2b009b1-e53a-4bcb-8694-20dae91cbd86
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 任务 f2b009b1-e53a-4bcb-8694-20dae91cbd86: 音频数量=2, 语言=zh
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.11秒
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 任务 f2b009b1-e53a-4bcb-8694-20dae91cbd86 完成，处理时间: 0.31秒，结果数量: 2
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: d3199888-c197-4d83-bdeb-99422514e4e5
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 d3199888-c197-4d83-bdeb-99422514e4e5: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.33秒，推理耗时: 0.10秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 d3199888-c197-4d83-bdeb-99422514e4e5 完成，处理时间: 0.33秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 65bdc7d3-2e17-49a7-9bc9-65fad6fa9053
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 65bdc7d3-2e17-49a7-9bc9-65fad6fa9053: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.08秒，推理耗时: 0.07秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 65bdc7d3-2e17-49a7-9bc9-65fad6fa9053 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 75160baa-0440-4497-bf90-21594bc2dfb1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 75160baa-0440-4497-bf90-21594bc2dfb1: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.08秒，推理耗时: 0.08秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 75160baa-0440-4497-bf90-21594bc2dfb1 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: a68e35ee-596a-462a-8f8a-bd575b7f75db
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 a68e35ee-596a-462a-8f8a-bd575b7f75db: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.08秒，推理耗时: 0.07秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 a68e35ee-596a-462a-8f8a-bd575b7f75db 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 30517b3c-f316-490a-b846-fe12ca1bf107
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 30517b3c-f316-490a-b846-fe12ca1bf107: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.30秒
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 任务 30517b3c-f316-490a-b846-fe12ca1bf107 完成，处理时间: 0.32秒，结果数量: 1
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 899b42b0-e388-452b-a890-efc8c443fd0f
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 任务 899b42b0-e388-452b-a890-efc8c443fd0f: 音频数量=5, 语言=auto
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 BAC009S0764W0179.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 BAC009S0916W0481.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 vad_example.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 5, 语言: auto
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.56秒，推理耗时: 0.34秒
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 任务 899b42b0-e388-452b-a890-efc8c443fd0f 完成，处理时间: 0.56秒，结果数量: 5
[2025-06-06 15:35:58][MainThread][INFO]: ==================================================
[2025-06-06 15:35:58][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:35:58][MainThread][INFO]: ==================================================
[2025-06-06 15:35:58][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:35:58][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:35:58][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:35:58][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:36:03][MainThread][INFO]: 模型加载成功，耗时: 5.09秒
[2025-06-06 15:36:03][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:36:03][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:36:03][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:36:03][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:36:03][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:36:03][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:36:03][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:36:03][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:36:12][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 704934a5-5b09-42e1-8967-fbc820c2c484
[2025-06-06 15:36:12][ThreadPoolExecutor-0_0][INFO]: 任务 704934a5-5b09-42e1-8967-fbc820c2c484: 音频数量=1, 语言=zh
[2025-06-06 15:36:15][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:36:15][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.31秒，推理耗时: 0.13秒
[2025-06-06 15:36:15][ThreadPoolExecutor-0_0][INFO]: 任务 704934a5-5b09-42e1-8967-fbc820c2c484 完成，处理时间: 3.32秒，结果数量: 1
[2025-06-06 15:37:41][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:37:41][MainThread][INFO]: 服务已停止
[2025-06-06 15:37:45][MainThread][INFO]: ==================================================
[2025-06-06 15:37:45][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:37:45][MainThread][INFO]: ==================================================
[2025-06-06 15:37:45][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:37:45][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:37:45][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:37:45][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:37:50][MainThread][INFO]: 模型加载成功，耗时: 5.09秒
[2025-06-06 15:37:50][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:37:50][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:37:50][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:37:50][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:37:50][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:37:50][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:37:50][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:37:50][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f545ff5e-f467-46f5-bc4e-eed66f8a5ce4
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 任务 f545ff5e-f467-46f5-bc4e-eed66f8a5ce4: 音频数量=1, 语言=zh
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.33秒，推理耗时: 0.14秒
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 任务 f545ff5e-f467-46f5-bc4e-eed66f8a5ce4 完成，处理时间: 0.33秒，结果数量: 1
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ee1fdfc3-9302-4f28-a309-1a4a35902038
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 任务 ee1fdfc3-9302-4f28-a309-1a4a35902038: 音频数量=1, 语言=zh
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.25秒，推理耗时: 0.09秒
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 任务 ee1fdfc3-9302-4f28-a309-1a4a35902038 完成，处理时间: 0.26秒，结果数量: 1
[2025-06-06 15:40:23][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:40:23][MainThread][INFO]: 服务已停止
[2025-06-06 15:42:33][MainThread][INFO]: ==================================================
[2025-06-06 15:42:33][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:42:33][MainThread][INFO]: ==================================================
[2025-06-06 15:42:33][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:42:33][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:42:33][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:42:33][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:42:38][MainThread][INFO]: 模型加载成功，耗时: 5.13秒
[2025-06-06 15:42:38][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:42:38][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:42:38][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:42:38][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:42:38][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:42:38][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:42:38][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:42:38][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:42:45][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ab823146-1849-4f43-83fa-11aaba2b8064
[2025-06-06 15:42:45][ThreadPoolExecutor-0_0][INFO]: 任务 ab823146-1849-4f43-83fa-11aaba2b8064: 音频数量=2, 语言=zh
[2025-06-06 15:42:45][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:42:45][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:42:45][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.34秒，推理耗时: 0.17秒
[2025-06-06 15:42:45][ThreadPoolExecutor-0_0][INFO]: 任务 ab823146-1849-4f43-83fa-11aaba2b8064 完成，处理时间: 0.34秒，结果数量: 2
[2025-06-06 15:43:35][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 33c581e1-7810-4283-a659-3acc733ed986
[2025-06-06 15:43:35][ThreadPoolExecutor-0_0][INFO]: 任务 33c581e1-7810-4283-a659-3acc733ed986: 音频数量=2, 语言=zh
[2025-06-06 15:43:35][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:43:35][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:43:35][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.32秒，推理耗时: 0.12秒
[2025-06-06 15:43:35][ThreadPoolExecutor-0_0][INFO]: 任务 33c581e1-7810-4283-a659-3acc733ed986 完成，处理时间: 0.33秒，结果数量: 2
[2025-06-06 15:47:13][MainThread][INFO]: ==================================================
[2025-06-06 15:47:13][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:47:13][MainThread][INFO]: ==================================================
[2025-06-06 15:47:13][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:47:13][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:47:13][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:47:13][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:47:18][MainThread][INFO]: 模型加载成功，耗时: 5.02秒
[2025-06-06 15:47:18][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:47:18][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:47:18][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:47:18][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:47:18][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:47:18][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:47:18][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:47:18][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:47:26][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 90314dc0-5fd4-4b8f-aec2-d3f6087bbcc1
[2025-06-06 15:47:26][ThreadPoolExecutor-0_0][INFO]: 任务 90314dc0-5fd4-4b8f-aec2-d3f6087bbcc1: 音频数量=1, 语言=zh
[2025-06-06 15:47:27][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:27][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.30秒，推理耗时: 0.13秒
[2025-06-06 15:47:27][ThreadPoolExecutor-0_0][INFO]: 任务 90314dc0-5fd4-4b8f-aec2-d3f6087bbcc1 完成，处理时间: 0.30秒，结果数量: 1
[2025-06-06 15:47:40][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 94e32b58-f182-4ed8-8160-fce8cdcdd0c7
[2025-06-06 15:47:40][ThreadPoolExecutor-0_0][INFO]: 任务 94e32b58-f182-4ed8-8160-fce8cdcdd0c7: 音频数量=1, 语言=zh
[2025-06-06 15:47:43][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:43][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.25秒，推理耗时: 0.10秒
[2025-06-06 15:47:43][ThreadPoolExecutor-0_0][INFO]: 任务 94e32b58-f182-4ed8-8160-fce8cdcdd0c7 完成，处理时间: 3.25秒，结果数量: 1
[2025-06-06 15:47:48][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ca3e0434-7b48-41c3-8373-29b46237806f
[2025-06-06 15:47:48][ThreadPoolExecutor-0_0][INFO]: 任务 ca3e0434-7b48-41c3-8373-29b46237806f: 音频数量=1, 语言=zh
[2025-06-06 15:47:48][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:48][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.27秒，推理耗时: 0.09秒
[2025-06-06 15:47:48][ThreadPoolExecutor-0_0][INFO]: 任务 ca3e0434-7b48-41c3-8373-29b46237806f 完成，处理时间: 0.27秒，结果数量: 1
[2025-06-06 15:47:51][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 7e9d31db-1096-455e-9a8e-58bb30f03f28
[2025-06-06 15:47:51][ThreadPoolExecutor-0_0][INFO]: 任务 7e9d31db-1096-455e-9a8e-58bb30f03f28: 音频数量=1, 语言=zh
[2025-06-06 15:47:51][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:51][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.25秒，推理耗时: 0.09秒
[2025-06-06 15:47:51][ThreadPoolExecutor-0_0][INFO]: 任务 7e9d31db-1096-455e-9a8e-58bb30f03f28 完成，处理时间: 0.25秒，结果数量: 1
[2025-06-06 15:47:52][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 4586a3cc-f6fb-4788-bd4f-62ebcb4fe7c3
[2025-06-06 15:47:52][ThreadPoolExecutor-0_0][INFO]: 任务 4586a3cc-f6fb-4788-bd4f-62ebcb4fe7c3: 音频数量=1, 语言=zh
[2025-06-06 15:47:53][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:53][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.77秒，推理耗时: 0.09秒
[2025-06-06 15:47:53][ThreadPoolExecutor-0_0][INFO]: 任务 4586a3cc-f6fb-4788-bd4f-62ebcb4fe7c3 完成，处理时间: 0.77秒，结果数量: 1
[2025-06-06 15:47:54][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 90be4aee-4eed-414e-87c3-4cf8d92b0734
[2025-06-06 15:47:54][ThreadPoolExecutor-0_0][INFO]: 任务 90be4aee-4eed-414e-87c3-4cf8d92b0734: 音频数量=1, 语言=zh
[2025-06-06 15:47:54][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:54][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.26秒，推理耗时: 0.09秒
[2025-06-06 15:47:54][ThreadPoolExecutor-0_0][INFO]: 任务 90be4aee-4eed-414e-87c3-4cf8d92b0734 完成，处理时间: 0.26秒，结果数量: 1
[2025-06-06 15:47:56][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: e350486b-8d67-4ba5-8faa-1e3bbe44df06
[2025-06-06 15:47:56][ThreadPoolExecutor-0_0][INFO]: 任务 e350486b-8d67-4ba5-8faa-1e3bbe44df06: 音频数量=1, 语言=zh
[2025-06-06 15:47:56][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:56][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.28秒，推理耗时: 0.08秒
[2025-06-06 15:47:56][ThreadPoolExecutor-0_0][INFO]: 任务 e350486b-8d67-4ba5-8faa-1e3bbe44df06 完成，处理时间: 0.28秒，结果数量: 1
[2025-06-06 15:47:58][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ef351db6-fe0d-454d-bfe1-9a1fe5c72f62
[2025-06-06 15:47:58][ThreadPoolExecutor-0_0][INFO]: 任务 ef351db6-fe0d-454d-bfe1-9a1fe5c72f62: 音频数量=1, 语言=zh
[2025-06-06 15:47:58][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:47:58][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.25秒，推理耗时: 0.09秒
[2025-06-06 15:47:58][ThreadPoolExecutor-0_0][INFO]: 任务 ef351db6-fe0d-454d-bfe1-9a1fe5c72f62 完成，处理时间: 0.25秒，结果数量: 1
[2025-06-06 15:48:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 7ee5aa79-f73f-48e3-a710-c8ae7b2209eb
[2025-06-06 15:48:00][ThreadPoolExecutor-0_0][INFO]: 任务 7ee5aa79-f73f-48e3-a710-c8ae7b2209eb: 音频数量=1, 语言=zh
[2025-06-06 15:48:00][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:00][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.27秒，推理耗时: 0.11秒
[2025-06-06 15:48:00][ThreadPoolExecutor-0_0][INFO]: 任务 7ee5aa79-f73f-48e3-a710-c8ae7b2209eb 完成，处理时间: 0.27秒，结果数量: 1
[2025-06-06 15:48:01][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 8db4aad3-0b3e-4c33-8571-6ffb81032cd0
[2025-06-06 15:48:01][ThreadPoolExecutor-0_0][INFO]: 任务 8db4aad3-0b3e-4c33-8571-6ffb81032cd0: 音频数量=1, 语言=zh
[2025-06-06 15:48:02][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:02][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.68秒，推理耗时: 0.09秒
[2025-06-06 15:48:02][ThreadPoolExecutor-0_0][INFO]: 任务 8db4aad3-0b3e-4c33-8571-6ffb81032cd0 完成，处理时间: 0.68秒，结果数量: 1
[2025-06-06 15:48:14][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 59e81b16-bb99-48da-8757-ea878a8d5f6e
[2025-06-06 15:48:14][ThreadPoolExecutor-0_0][INFO]: 任务 59e81b16-bb99-48da-8757-ea878a8d5f6e: 音频数量=1, 语言=zh
[2025-06-06 15:48:14][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:14][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.30秒，推理耗时: 0.09秒
[2025-06-06 15:48:14][ThreadPoolExecutor-0_0][INFO]: 任务 59e81b16-bb99-48da-8757-ea878a8d5f6e 完成，处理时间: 0.30秒，结果数量: 1
[2025-06-06 15:48:16][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c8d2a79a-ce5a-4b9e-b6b7-963ad7a7964f
[2025-06-06 15:48:16][ThreadPoolExecutor-0_0][INFO]: 任务 c8d2a79a-ce5a-4b9e-b6b7-963ad7a7964f: 音频数量=1, 语言=zh
[2025-06-06 15:48:16][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:17][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.86秒，推理耗时: 0.08秒
[2025-06-06 15:48:17][ThreadPoolExecutor-0_0][INFO]: 任务 c8d2a79a-ce5a-4b9e-b6b7-963ad7a7964f 完成，处理时间: 0.86秒，结果数量: 1
[2025-06-06 15:48:18][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: d5fea5f2-4125-4b75-91e1-66450c8765b6
[2025-06-06 15:48:18][ThreadPoolExecutor-0_0][INFO]: 任务 d5fea5f2-4125-4b75-91e1-66450c8765b6: 音频数量=1, 语言=zh
[2025-06-06 15:48:18][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:18][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.30秒，推理耗时: 0.11秒
[2025-06-06 15:48:18][ThreadPoolExecutor-0_0][INFO]: 任务 d5fea5f2-4125-4b75-91e1-66450c8765b6 完成，处理时间: 0.31秒，结果数量: 1
[2025-06-06 15:48:19][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f7365555-88c0-46fb-8719-d5b01230f90d
[2025-06-06 15:48:19][ThreadPoolExecutor-0_0][INFO]: 任务 f7365555-88c0-46fb-8719-d5b01230f90d: 音频数量=1, 语言=zh
[2025-06-06 15:48:20][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:20][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.67秒，推理耗时: 0.09秒
[2025-06-06 15:48:20][ThreadPoolExecutor-0_0][INFO]: 任务 f7365555-88c0-46fb-8719-d5b01230f90d 完成，处理时间: 0.68秒，结果数量: 1
[2025-06-06 15:48:21][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 51447cd3-d7d0-44e3-be5a-a4fb418a88cd
[2025-06-06 15:48:21][ThreadPoolExecutor-0_0][INFO]: 任务 51447cd3-d7d0-44e3-be5a-a4fb418a88cd: 音频数量=1, 语言=zh
[2025-06-06 15:48:21][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:21][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.79秒，推理耗时: 0.11秒
[2025-06-06 15:48:21][ThreadPoolExecutor-0_0][INFO]: 任务 51447cd3-d7d0-44e3-be5a-a4fb418a88cd 完成，处理时间: 0.79秒，结果数量: 1
[2025-06-06 15:48:23][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 15c2e7b5-f26b-4005-ab67-2a6b23f596ef
[2025-06-06 15:48:23][ThreadPoolExecutor-0_0][INFO]: 任务 15c2e7b5-f26b-4005-ab67-2a6b23f596ef: 音频数量=1, 语言=zh
[2025-06-06 15:48:23][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:48:23][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.79秒，推理耗时: 0.09秒
[2025-06-06 15:48:23][ThreadPoolExecutor-0_0][INFO]: 任务 15c2e7b5-f26b-4005-ab67-2a6b23f596ef 完成，处理时间: 0.79秒，结果数量: 1
[2025-06-06 15:49:22][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ab33eb0b-cd69-4c02-a015-964cbc1fed9a
[2025-06-06 15:49:22][ThreadPoolExecutor-0_0][INFO]: 任务 ab33eb0b-cd69-4c02-a015-964cbc1fed9a: 音频数量=1, 语言=zh
[2025-06-06 15:49:25][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:49:25][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.26秒，推理耗时: 0.10秒
[2025-06-06 15:49:25][ThreadPoolExecutor-0_0][INFO]: 任务 ab33eb0b-cd69-4c02-a015-964cbc1fed9a 完成，处理时间: 3.26秒，结果数量: 1
[2025-06-06 15:49:28][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: de86fc67-436d-4890-80df-60ea8f0bf0ec
[2025-06-06 15:49:28][ThreadPoolExecutor-0_0][INFO]: 任务 de86fc67-436d-4890-80df-60ea8f0bf0ec: 音频数量=1, 语言=zh
[2025-06-06 15:49:29][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:49:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 1.48秒，推理耗时: 0.09秒
[2025-06-06 15:49:30][ThreadPoolExecutor-0_0][INFO]: 任务 de86fc67-436d-4890-80df-60ea8f0bf0ec 完成，处理时间: 1.48秒，结果数量: 1
[2025-06-06 15:49:34][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 55ff2acb-1a2b-432e-85c6-fb88e7e498b0
[2025-06-06 15:49:34][ThreadPoolExecutor-0_0][INFO]: 任务 55ff2acb-1a2b-432e-85c6-fb88e7e498b0: 音频数量=1, 语言=zh
[2025-06-06 15:49:34][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:49:34][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.24秒，推理耗时: 0.09秒
[2025-06-06 15:49:34][ThreadPoolExecutor-0_0][INFO]: 任务 55ff2acb-1a2b-432e-85c6-fb88e7e498b0 完成，处理时间: 0.24秒，结果数量: 1
[2025-06-06 15:50:36][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: a5894bab-88be-496d-a1fd-d410c46362d9
[2025-06-06 15:50:36][ThreadPoolExecutor-0_0][INFO]: 任务 a5894bab-88be-496d-a1fd-d410c46362d9: 音频数量=1, 语言=zh
[2025-06-06 15:50:36][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:50:37][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.25秒，推理耗时: 0.09秒
[2025-06-06 15:50:37][ThreadPoolExecutor-0_0][INFO]: 任务 a5894bab-88be-496d-a1fd-d410c46362d9 完成，处理时间: 0.25秒，结果数量: 1
[2025-06-06 15:50:38][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: e3f747b5-b259-46cd-8fc1-b8b1d15993d0
[2025-06-06 15:50:38][ThreadPoolExecutor-0_0][INFO]: 任务 e3f747b5-b259-46cd-8fc1-b8b1d15993d0: 音频数量=1, 语言=zh
[2025-06-06 15:50:38][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:50:38][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.24秒，推理耗时: 0.09秒
[2025-06-06 15:50:38][ThreadPoolExecutor-0_0][INFO]: 任务 e3f747b5-b259-46cd-8fc1-b8b1d15993d0 完成，处理时间: 0.24秒，结果数量: 1
[2025-06-06 15:51:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: b9921892-f9a9-4535-b142-376df1e5dfb8
[2025-06-06 15:51:00][ThreadPoolExecutor-0_0][INFO]: 任务 b9921892-f9a9-4535-b142-376df1e5dfb8: 音频数量=1, 语言=zh
[2025-06-06 15:51:01][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:51:01][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.28秒，推理耗时: 0.10秒
[2025-06-06 15:51:01][ThreadPoolExecutor-0_0][INFO]: 任务 b9921892-f9a9-4535-b142-376df1e5dfb8 完成，处理时间: 0.28秒，结果数量: 1
[2025-06-06 15:51:02][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1f42b878-893e-44ad-a0d1-de8ca9fcb07a
[2025-06-06 15:51:02][ThreadPoolExecutor-0_0][INFO]: 任务 1f42b878-893e-44ad-a0d1-de8ca9fcb07a: 音频数量=1, 语言=zh
[2025-06-06 15:51:02][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:51:02][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.33秒，推理耗时: 0.12秒
[2025-06-06 15:51:02][ThreadPoolExecutor-0_0][INFO]: 任务 1f42b878-893e-44ad-a0d1-de8ca9fcb07a 完成，处理时间: 0.33秒，结果数量: 1
[2025-06-06 15:51:06][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 90bf3d98-09c0-4cbd-bb45-3323b397cfcf
[2025-06-06 15:51:06][ThreadPoolExecutor-0_0][INFO]: 任务 90bf3d98-09c0-4cbd-bb45-3323b397cfcf: 音频数量=1, 语言=zh
[2025-06-06 15:51:08][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:51:08][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 1.76秒，推理耗时: 0.09秒
[2025-06-06 15:51:08][ThreadPoolExecutor-0_0][INFO]: 任务 90bf3d98-09c0-4cbd-bb45-3323b397cfcf 完成，处理时间: 1.76秒，结果数量: 1
[2025-06-06 15:51:12][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 4c6af8d1-8469-4a8f-81d8-45d5e27feb0d
[2025-06-06 15:51:12][ThreadPoolExecutor-0_0][INFO]: 任务 4c6af8d1-8469-4a8f-81d8-45d5e27feb0d: 音频数量=1, 语言=zh
[2025-06-06 15:51:13][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:51:13][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.24秒，推理耗时: 0.09秒
[2025-06-06 15:51:13][ThreadPoolExecutor-0_0][INFO]: 任务 4c6af8d1-8469-4a8f-81d8-45d5e27feb0d 完成，处理时间: 0.24秒，结果数量: 1
[2025-06-06 15:58:29][MainThread][INFO]: ==================================================
[2025-06-06 15:58:29][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:58:29][MainThread][INFO]: ==================================================
[2025-06-06 15:58:29][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:58:29][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:58:29][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:58:29][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:58:34][MainThread][INFO]: 模型加载成功，耗时: 5.47秒
[2025-06-06 15:58:34][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:58:34][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:58:34][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:58:34][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:58:34][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:58:34][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:58:34][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:58:34][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:59:04][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:59:04][MainThread][INFO]: 服务已停止
[2025-06-06 15:59:10][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: a320577a-4a42-41b2-9d67-3c1a80dcb1fe
[2025-06-06 15:59:10][ThreadPoolExecutor-0_0][INFO]: 任务 a320577a-4a42-41b2-9d67-3c1a80dcb1fe: 音频数量=2, 语言=zh
[2025-06-06 15:59:10][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:59:10][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:59:10][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.365秒 | 验证: 0.000秒 | 音频加载: 0.189秒 | 模型推理: 0.175秒 | 结果处理: 0.001秒
[2025-06-06 15:59:10][ThreadPoolExecutor-0_0][INFO]: 任务 a320577a-4a42-41b2-9d67-3c1a80dcb1fe 完成，处理时间: 0.37秒，结果数量: 2
[2025-06-06 15:59:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: b2c6807d-523e-430a-9b79-397bab4d25f4
[2025-06-06 15:59:30][ThreadPoolExecutor-0_0][INFO]: 任务 b2c6807d-523e-430a-9b79-397bab4d25f4: 音频数量=1, 语言=zh
[2025-06-06 15:59:30][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:59:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.263秒 | 验证: 0.000秒 | 音频加载: 0.172秒 | 模型推理: 0.090秒 | 结果处理: 0.001秒
[2025-06-06 15:59:30][ThreadPoolExecutor-0_0][INFO]: 任务 b2c6807d-523e-430a-9b79-397bab4d25f4 完成，处理时间: 0.26秒，结果数量: 1
[2025-06-06 15:59:31][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 07ac6789-db4b-460c-a0eb-bcf9fd8ea7ab
[2025-06-06 15:59:31][ThreadPoolExecutor-0_0][INFO]: 任务 07ac6789-db4b-460c-a0eb-bcf9fd8ea7ab: 音频数量=1, 语言=zh
[2025-06-06 15:59:31][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:59:31][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.287秒 | 验证: 0.000秒 | 音频加载: 0.192秒 | 模型推理: 0.093秒 | 结果处理: 0.001秒
[2025-06-06 15:59:31][ThreadPoolExecutor-0_0][WARNING]: 音频加载耗时(0.192秒)明显高于推理耗时(0.093秒)，可能存在性能瓶颈
[2025-06-06 15:59:31][ThreadPoolExecutor-0_0][INFO]: 任务 07ac6789-db4b-460c-a0eb-bcf9fd8ea7ab 完成，处理时间: 0.29秒，结果数量: 1
[2025-06-06 15:59:32][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 4508254a-a161-4a80-bef8-3e601a66c628
[2025-06-06 15:59:32][ThreadPoolExecutor-0_0][INFO]: 任务 4508254a-a161-4a80-bef8-3e601a66c628: 音频数量=1, 语言=zh
[2025-06-06 15:59:32][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:59:32][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.275秒 | 验证: 0.000秒 | 音频加载: 0.186秒 | 模型推理: 0.088秒 | 结果处理: 0.000秒
[2025-06-06 15:59:32][ThreadPoolExecutor-0_0][WARNING]: 音频加载耗时(0.186秒)明显高于推理耗时(0.088秒)，可能存在性能瓶颈
[2025-06-06 15:59:32][ThreadPoolExecutor-0_0][INFO]: 任务 4508254a-a161-4a80-bef8-3e601a66c628 完成，处理时间: 0.28秒，结果数量: 1
[2025-06-06 16:05:20][MainThread][INFO]: ==================================================
[2025-06-06 16:05:20][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:05:20][MainThread][INFO]: ==================================================
[2025-06-06 16:05:20][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:05:20][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 16:05:20][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 16:05:20][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 16:05:26][MainThread][INFO]: 模型加载成功，耗时: 5.42秒
[2025-06-06 16:05:26][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 16:05:26][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 16:05:26][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 16:05:26][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 16:05:26][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 16:05:26][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 16:05:26][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 16:05:26][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 16:05:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: a40c319c-1a52-4d08-9c6d-3beea63eae4b
[2025-06-06 16:05:30][ThreadPoolExecutor-0_0][INFO]: 任务 a40c319c-1a52-4d08-9c6d-3beea63eae4b: 音频数量=1, 语言=zh
[2025-06-06 16:05:30][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:05:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.274秒 | 验证: 0.000秒 | 音频加载: 0.148秒 | 模型推理: 0.124秒 | 结果处理: 0.001秒
[2025-06-06 16:05:30][ThreadPoolExecutor-0_0][INFO]: 任务 a40c319c-1a52-4d08-9c6d-3beea63eae4b 完成，处理时间: 0.27秒，结果数量: 1
[2025-06-06 16:05:53][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0ec144dd-7efc-490e-b783-39184a3a1339
[2025-06-06 16:05:53][ThreadPoolExecutor-0_0][INFO]: 任务 0ec144dd-7efc-490e-b783-39184a3a1339: 音频数量=1, 语言=zh
[2025-06-06 16:05:53][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:05:53][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.249秒 | 验证: 0.000秒 | 音频加载: 0.140秒 | 模型推理: 0.108秒 | 结果处理: 0.001秒
[2025-06-06 16:05:53][ThreadPoolExecutor-0_0][INFO]: 任务 0ec144dd-7efc-490e-b783-39184a3a1339 完成，处理时间: 0.25秒，结果数量: 1
[2025-06-06 16:05:55][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f3fb7a1f-99bc-4bca-812c-0958e6e39256
[2025-06-06 16:05:55][ThreadPoolExecutor-0_0][INFO]: 任务 f3fb7a1f-99bc-4bca-812c-0958e6e39256: 音频数量=1, 语言=zh
[2025-06-06 16:05:56][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:05:56][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.940秒 | 验证: 0.000秒 | 音频加载: 0.849秒 | 模型推理: 0.091秒 | 结果处理: 0.000秒
[2025-06-06 16:05:56][ThreadPoolExecutor-0_0][WARNING]: 音频加载耗时(0.849秒)明显高于推理耗时(0.091秒)，可能存在性能瓶颈
[2025-06-06 16:05:56][ThreadPoolExecutor-0_0][INFO]: 任务 f3fb7a1f-99bc-4bca-812c-0958e6e39256 完成，处理时间: 0.94秒，结果数量: 1
[2025-06-06 16:05:58][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 29638bb8-7f64-4a91-ab1d-5864c2f5bc11
[2025-06-06 16:05:58][ThreadPoolExecutor-0_0][INFO]: 任务 29638bb8-7f64-4a91-ab1d-5864c2f5bc11: 音频数量=1, 语言=zh
[2025-06-06 16:05:58][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:05:58][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.228秒 | 验证: 0.000秒 | 音频加载: 0.141秒 | 模型推理: 0.086秒 | 结果处理: 0.000秒
[2025-06-06 16:05:58][ThreadPoolExecutor-0_0][INFO]: 任务 29638bb8-7f64-4a91-ab1d-5864c2f5bc11 完成，处理时间: 0.23秒，结果数量: 1
[2025-06-06 16:06:28][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1f6039a4-5fe5-497b-bfef-d3d21cfc183f
[2025-06-06 16:06:28][ThreadPoolExecutor-0_0][INFO]: 任务 1f6039a4-5fe5-497b-bfef-d3d21cfc183f: 音频数量=1, 语言=zh
[2025-06-06 16:06:28][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:06:28][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.078秒 | 验证: 0.000秒 | 音频加载: 0.001秒 | 模型推理: 0.077秒 | 结果处理: 0.000秒
[2025-06-06 16:06:28][ThreadPoolExecutor-0_0][INFO]: 任务 1f6039a4-5fe5-497b-bfef-d3d21cfc183f 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 16:06:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0632ab6d-55d8-4a90-a1d0-092af5730d44
[2025-06-06 16:06:30][ThreadPoolExecutor-0_0][INFO]: 任务 0632ab6d-55d8-4a90-a1d0-092af5730d44: 音频数量=1, 语言=zh
[2025-06-06 16:06:30][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:06:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.075秒 | 验证: 0.000秒 | 音频加载: 0.001秒 | 模型推理: 0.073秒 | 结果处理: 0.000秒
[2025-06-06 16:06:30][ThreadPoolExecutor-0_0][INFO]: 任务 0632ab6d-55d8-4a90-a1d0-092af5730d44 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 16:06:31][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 731bc15d-97e1-4087-af79-1c727ff3c856
[2025-06-06 16:06:31][ThreadPoolExecutor-0_0][INFO]: 任务 731bc15d-97e1-4087-af79-1c727ff3c856: 音频数量=1, 语言=zh
[2025-06-06 16:06:31][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:06:31][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.066秒 | 验证: 0.000秒 | 音频加载: 0.001秒 | 模型推理: 0.066秒 | 结果处理: 0.000秒
[2025-06-06 16:06:31][ThreadPoolExecutor-0_0][INFO]: 任务 731bc15d-97e1-4087-af79-1c727ff3c856 完成，处理时间: 0.07秒，结果数量: 1
[2025-06-06 16:06:32][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 6d75d1e3-4d80-4bdb-be52-fc620e34a809
[2025-06-06 16:06:32][ThreadPoolExecutor-0_0][INFO]: 任务 6d75d1e3-4d80-4bdb-be52-fc620e34a809: 音频数量=1, 语言=zh
[2025-06-06 16:06:32][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:06:32][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.071秒 | 验证: 0.000秒 | 音频加载: 0.001秒 | 模型推理: 0.069秒 | 结果处理: 0.000秒
[2025-06-06 16:06:32][ThreadPoolExecutor-0_0][INFO]: 任务 6d75d1e3-4d80-4bdb-be52-fc620e34a809 完成，处理时间: 0.07秒，结果数量: 1
[2025-06-06 16:06:47][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 77e7b83a-d9df-4b85-aef3-b891b652c3be
[2025-06-06 16:06:47][ThreadPoolExecutor-0_0][INFO]: 任务 77e7b83a-d9df-4b85-aef3-b891b652c3be: 音频数量=1, 语言=zh
[2025-06-06 16:06:47][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:06:47][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.298秒 | 验证: 0.000秒 | 音频加载: 0.011秒 | 模型推理: 0.285秒 | 结果处理: 0.001秒
[2025-06-06 16:06:47][ThreadPoolExecutor-0_0][INFO]: 任务 77e7b83a-d9df-4b85-aef3-b891b652c3be 完成，处理时间: 0.30秒，结果数量: 1
[2025-06-06 16:06:48][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 3548d726-606f-457b-be14-e4819a66c0ef
[2025-06-06 16:06:48][ThreadPoolExecutor-0_0][INFO]: 任务 3548d726-606f-457b-be14-e4819a66c0ef: 音频数量=1, 语言=zh
[2025-06-06 16:06:48][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:06:49][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.274秒 | 验证: 0.000秒 | 音频加载: 0.008秒 | 模型推理: 0.266秒 | 结果处理: 0.000秒
[2025-06-06 16:06:49][ThreadPoolExecutor-0_0][INFO]: 任务 3548d726-606f-457b-be14-e4819a66c0ef 完成，处理时间: 0.28秒，结果数量: 1
[2025-06-06 16:07:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: b1333cc2-5b16-4b6e-991c-0207435c254a
[2025-06-06 16:07:00][ThreadPoolExecutor-0_0][INFO]: 任务 b1333cc2-5b16-4b6e-991c-0207435c254a: 音频数量=1, 语言=zh
[2025-06-06 16:07:00][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:07:00][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.258秒 | 验证: 0.000秒 | 音频加载: 0.008秒 | 模型推理: 0.250秒 | 结果处理: 0.000秒
[2025-06-06 16:07:00][ThreadPoolExecutor-0_0][INFO]: 任务 b1333cc2-5b16-4b6e-991c-0207435c254a 完成，处理时间: 0.26秒，结果数量: 1
[2025-06-06 16:07:07][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 4fa9f801-6c02-49ad-97ab-ff268892b042
[2025-06-06 16:07:07][ThreadPoolExecutor-0_0][INFO]: 任务 4fa9f801-6c02-49ad-97ab-ff268892b042: 音频数量=1, 语言=zh
[2025-06-06 16:07:07][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:07:08][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.286秒 | 验证: 0.000秒 | 音频加载: 0.010秒 | 模型推理: 0.275秒 | 结果处理: 0.001秒
[2025-06-06 16:07:08][ThreadPoolExecutor-0_0][INFO]: 任务 4fa9f801-6c02-49ad-97ab-ff268892b042 完成，处理时间: 0.29秒，结果数量: 1
[2025-06-06 16:08:13][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 16:08:13][MainThread][INFO]: 服务已停止
[2025-06-06 16:44:51][MainThread][INFO]: ==================================================
[2025-06-06 16:44:51][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:44:51][MainThread][INFO]: ==================================================
[2025-06-06 16:44:51][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:44:51][MainThread][INFO]: 推理模式: local
[2025-06-06 16:44:51][MainThread][INFO]: 使用LOCAL模式 - 本地SenseVoiceSmall模型
[2025-06-06 16:44:51][MainThread][ERROR]: 推理引擎加载失败: cannot import name 'SpeechInference' from partially initialized module 'speech_inference_local' (most likely due to a circular import) (/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference_local.py)
[2025-06-06 16:44:51][MainThread][ERROR]: 推理引擎初始化失败: cannot import name 'SpeechInference' from partially initialized module 'speech_inference_local' (most likely due to a circular import) (/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference_local.py)
[2025-06-06 16:46:07][MainThread][INFO]: ==================================================
[2025-06-06 16:46:07][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:46:07][MainThread][INFO]: ==================================================
[2025-06-06 16:46:07][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:46:07][MainThread][INFO]: 推理模式: local
[2025-06-06 16:46:07][MainThread][INFO]: 使用LOCAL模式 - 本地SenseVoiceSmall模型
[2025-06-06 16:46:07][MainThread][ERROR]: 推理引擎加载失败: cannot import name 'SpeechInference' from partially initialized module 'speech_inference_local' (most likely due to a circular import) (/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference_local.py)
[2025-06-06 16:46:07][MainThread][ERROR]: 推理引擎初始化失败: cannot import name 'SpeechInference' from partially initialized module 'speech_inference_local' (most likely due to a circular import) (/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference_local.py)
[2025-06-06 16:48:00][MainThread][INFO]: ==================================================
[2025-06-06 16:48:00][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:48:00][MainThread][INFO]: ==================================================
[2025-06-06 16:48:00][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:48:00][MainThread][INFO]: 推理模式: local
[2025-06-06 16:48:00][MainThread][INFO]: 使用LOCAL模式 - 本地SenseVoiceSmall模型
[2025-06-06 16:48:03][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 16:48:03][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 16:48:03][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 16:48:08][MainThread][INFO]: 模型加载成功，耗时: 5.17秒
[2025-06-06 16:48:08][MainThread][INFO]: 推理引擎加载成功，模式: local
[2025-06-06 16:48:08][MainThread][INFO]: 推理引擎信息: 本地SenseVoiceSmall模型 - 速度快，无需联网，但断句效果一般
[2025-06-06 16:48:08][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 16:48:08][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 16:48:08][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 16:48:08][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 16:48:08][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 16:48:08][MainThread][INFO]: 推理模式: local
[2025-06-06 16:48:08][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 16:48:08][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 16:48:08][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f769988a-f0db-4042-bcdf-0c5d2efebb7e
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 任务 f769988a-f0db-4042-bcdf-0c5d2efebb7e: 音频数量=1, 语言=zh
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.385秒 | 验证: 0.000秒 | 音频加载: 0.195秒 | 模型推理: 0.188秒 | 结果处理: 0.001秒
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 任务 f769988a-f0db-4042-bcdf-0c5d2efebb7e 完成，处理时间: 0.39秒，结果数量: 1
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0dd53bea-ab1d-4210-bac2-0035c29713ea
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 任务 0dd53bea-ab1d-4210-bac2-0035c29713ea: 音频数量=1, 语言=zh
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.127秒 | 验证: 0.000秒 | 音频加载: 0.001秒 | 模型推理: 0.126秒 | 结果处理: 0.000秒
[2025-06-06 16:48:58][ThreadPoolExecutor-0_0][INFO]: 任务 0dd53bea-ab1d-4210-bac2-0035c29713ea 完成，处理时间: 0.13秒，结果数量: 1
[2025-06-06 16:49:58][MainThread][INFO]: ==================================================
[2025-06-06 16:49:58][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:49:58][MainThread][INFO]: ==================================================
[2025-06-06 16:49:58][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:49:58][MainThread][INFO]: 推理模式: funasr
[2025-06-06 16:49:58][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 16:50:01][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 16:50:01][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 16:50:01][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 16:50:01][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 16:50:09][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 16:50:09][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 16:50:15][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 16:50:15][MainThread][ERROR]: 2025-06-06 16:50:15,436 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 16:50:15][MainThread][INFO]: funasr模型加载成功，耗时: 14.18秒
[2025-06-06 16:50:15][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 16:50:15][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 16:50:15][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 16:50:15][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 16:50:15][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 16:50:15][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 16:50:15][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 16:50:15][MainThread][INFO]: 推理模式: funasr
[2025-06-06 16:50:15][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 16:50:15][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 16:50:15][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: a3cfba82-65f6-494c-9fef-a641be3ade48
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 任务 a3cfba82-65f6-494c-9fef-a641be3ade48: 音频数量=1, 语言=zh
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.95it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.092', 'extract_feat': '0.008', 'forward': '0.144', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.95it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.95it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.84it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.008', 'forward': '0.062', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.07it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.92it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.31it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  7.59it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  13.704, time_escape: 0.070: 100%|[31m##########[0m| 1/1 [00:00<00:00,  7.59it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  13.704, time_escape: 0.070: 100%|[31m##########[0m| 1/1 [00:00<00:00,  7.57it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.282秒 | 验证: 0.000秒 | 文件准备: 0.001秒 | 模型推理: 0.281秒 | 清理: 0.000秒
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 任务 a3cfba82-65f6-494c-9fef-a641be3ade48 完成，处理时间: 0.28秒，结果数量: 1
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: e82b7189-41a1-46a7-a5e0-1afe25a700f7
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 任务 e82b7189-41a1-46a7-a5e0-1afe25a700f7: 音频数量=1, 语言=zh
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.003', 'forward': '0.020', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 49.59it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 48.68it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 46.95it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.004', 'forward': '0.062', 'batch_size': '1', 'rtf': '0.013'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.23it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013: 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.07it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.66it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013, time_speech:  5.547, time_escape: 0.070: 100%|[31m##########[0m| 1/1 [00:00<00:00, 14.02it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013, time_speech:  5.547, time_escape: 0.070: 100%|[31m##########[0m| 1/1 [00:00<00:00, 13.95it/s]
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.095秒 | 验证: 0.000秒 | 文件准备: 0.000秒 | 模型推理: 0.094秒 | 清理: 0.000秒
[2025-06-06 16:51:31][ThreadPoolExecutor-0_0][INFO]: 任务 e82b7189-41a1-46a7-a5e0-1afe25a700f7 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 16:53:52][MainThread][INFO]: ==================================================
[2025-06-06 16:53:52][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:53:52][MainThread][INFO]: ==================================================
[2025-06-06 16:53:52][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:53:52][MainThread][INFO]: 推理模式: local
[2025-06-06 16:53:52][MainThread][INFO]: 使用LOCAL模式 - 本地SenseVoiceSmall模型
[2025-06-06 16:53:55][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 16:53:55][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 16:53:55][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 16:54:00][MainThread][INFO]: 模型加载成功，耗时: 4.95秒
[2025-06-06 16:54:00][MainThread][INFO]: 推理引擎加载成功，模式: local
[2025-06-06 16:54:00][MainThread][INFO]: 推理引擎信息: 本地SenseVoiceSmall模型 - 速度快，无需联网，但断句效果一般
[2025-06-06 16:54:00][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 16:54:00][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 16:54:00][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 16:54:00][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 16:54:00][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 16:54:00][MainThread][INFO]: 推理模式: local
[2025-06-06 16:54:00][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 16:54:00][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 16:54:00][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 16:54:03][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: e024466b-b0df-4f37-a9b3-fc055ecca947
[2025-06-06 16:54:03][ThreadPoolExecutor-0_0][INFO]: 任务 e024466b-b0df-4f37-a9b3-fc055ecca947: 音频数量=1, 语言=zh
[2025-06-06 16:54:03][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:54:04][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.313秒 | 验证: 0.000秒 | 音频加载: 0.013秒 | 模型推理: 0.299秒 | 结果处理: 0.001秒
[2025-06-06 16:54:04][ThreadPoolExecutor-0_0][INFO]: 任务 e024466b-b0df-4f37-a9b3-fc055ecca947 完成，处理时间: 0.32秒，结果数量: 1
[2025-06-06 16:54:10][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 16:54:10][MainThread][INFO]: 服务已停止
[2025-06-06 16:54:24][MainThread][INFO]: ==================================================
[2025-06-06 16:54:24][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:54:24][MainThread][INFO]: ==================================================
[2025-06-06 16:54:24][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:54:24][MainThread][INFO]: 推理模式: funasr
[2025-06-06 16:54:24][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 16:54:27][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 16:54:27][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 16:54:27][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 16:54:27][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 16:54:27][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 16:54:27][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 16:54:33][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 16:54:33][MainThread][ERROR]: 2025-06-06 16:54:33,410 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 16:54:33][MainThread][INFO]: funasr模型加载成功，耗时: 6.63秒
[2025-06-06 16:54:33][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 16:54:33][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 16:54:33][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 16:54:33][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 16:54:33][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 16:54:33][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 16:54:33][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 16:54:33][MainThread][INFO]: 推理模式: funasr
[2025-06-06 16:54:33][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 16:54:33][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 16:54:33][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ae597a9e-efb4-484a-aa32-03bc3b556534
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][INFO]: 任务 ae597a9e-efb4-484a-aa32-03bc3b556534: 音频数量=1, 语言=zh
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.37it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.013', 'extract_feat': '0.188', 'forward': '0.229', 'batch_size': '1', 'rtf': '0.022'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.37it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.37it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.30it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 29.44it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.038', 'forward': '0.170', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 29.44it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 29.44it/s]
[2025-06-06 16:54:37][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.63it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.014', 'forward': '0.066', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.10it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.96it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.25it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.62it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.268: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.62it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.268: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.62it/s]
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.514秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.512秒 | 清理: 0.000秒
[2025-06-06 16:54:38][ThreadPoolExecutor-0_0][INFO]: 任务 ae597a9e-efb4-484a-aa32-03bc3b556534 完成，处理时间: 0.52秒，结果数量: 1
[2025-06-06 16:58:46][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 16:58:46][MainThread][INFO]: 服务已停止
[2025-06-06 16:58:47][MainThread][INFO]: ==================================================
[2025-06-06 16:58:47][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 16:58:47][MainThread][INFO]: ==================================================
[2025-06-06 16:58:47][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 16:58:47][MainThread][INFO]: 推理模式: funasr
[2025-06-06 16:58:47][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 16:58:51][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 16:58:51][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 16:58:51][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 16:58:51][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 16:58:51][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 16:58:51][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 16:58:58][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 16:58:58][MainThread][ERROR]: 2025-06-06 16:58:58,260 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 16:58:58][MainThread][INFO]: funasr模型加载成功，耗时: 7.39秒
[2025-06-06 16:58:58][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 16:58:58][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 16:58:58][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 16:58:58][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 16:58:58][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 16:58:58][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 16:58:58][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 16:58:58][MainThread][INFO]: 推理模式: funasr
[2025-06-06 16:58:58][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 16:58:58][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 16:58:58][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 5c6b7845-7dde-4f60-862b-bd287d4a55de
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][INFO]: 任务 5c6b7845-7dde-4f60-862b-bd287d4a55de: 音频数量=1, 语言=zh
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.03it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.012', 'extract_feat': '0.210', 'forward': '0.248', 'batch_size': '1', 'rtf': '0.024'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.03it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.024: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.03it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.024: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.96it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.22it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.042', 'forward': '0.198', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.22it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.22it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 24.04it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.063', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.98it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.85it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.75it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.42it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.284: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.42it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.284: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.41it/s]
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.550秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.548秒 | 清理: 0.000秒
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: --- Logging error ---
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Traceback (most recent call last):
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/handlers.py", line 69, in emit
    if self.shouldRollover(record):
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/handlers.py", line 185, in shouldRollover
    msg = "%s\n" % self.format(record)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 929, in format
    return fmt.format(record)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 668, in format
    record.message = record.getMessage()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 373, in getMessage
    msg = msg % self.args
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: TypeError: not all arguments converted during string formatting
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Call stack:
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 80, in _worker
    work_item.run()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 793, in _unary_response_in_pool
    response, proceed = _call_behavior(
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 610, in _call_behavior
    response_or_iterator = behavior(argument, context)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 114, in RecognizeSpeech
    self.logger.info('推理的文本结果为：', results)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Message: '推理的文本结果为：'
Arguments: ([key: "vad_example.wav"
rawText: "<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实"
cleanText: "但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实"
text: "试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊"
duration: 70.470625
language: "zh"
],)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: --- Logging error ---
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Traceback (most recent call last):
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 1085, in emit
    msg = self.format(record)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 929, in format
    return fmt.format(record)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 668, in format
    record.message = record.getMessage()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 373, in getMessage
    msg = msg % self.args
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: TypeError: not all arguments converted during string formatting
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Call stack:
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 80, in _worker
    work_item.run()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 793, in _unary_response_in_pool
    response, proceed = _call_behavior(
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 610, in _call_behavior
    response_or_iterator = behavior(argument, context)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 114, in RecognizeSpeech
    self.logger.info('推理的文本结果为：', results)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Message: '推理的文本结果为：'
Arguments: ([key: "vad_example.wav"
rawText: "<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实"
cleanText: "但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实"
text: "试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊"
duration: 70.470625
language: "zh"
],)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: --- Logging error ---
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Traceback (most recent call last):
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 1085, in emit
    msg = self.format(record)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 929, in format
    return fmt.format(record)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 668, in format
    record.message = record.getMessage()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 373, in getMessage
    msg = msg % self.args
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: TypeError: not all arguments converted during string formatting
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Call stack:
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 80, in _worker
    work_item.run()
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 793, in _unary_response_in_pool
    response, proceed = _call_behavior(
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 610, in _call_behavior
    response_or_iterator = behavior(argument, context)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 114, in RecognizeSpeech
    self.logger.info('推理的文本结果为：', results)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][ERROR]: Message: '推理的文本结果为：'
Arguments: ([key: "vad_example.wav"
rawText: "<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实"
cleanText: "但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实"
text: "试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊"
duration: 70.470625
language: "zh"
],)
[2025-06-06 16:59:02][ThreadPoolExecutor-0_0][INFO]: 任务 5c6b7845-7dde-4f60-862b-bd287d4a55de 完成，处理时间: 0.55秒，结果数量: 1 

[2025-06-06 17:03:15][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:03:15][MainThread][INFO]: 服务已停止
[2025-06-06 17:03:16][MainThread][INFO]: ==================================================
[2025-06-06 17:03:16][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:03:16][MainThread][INFO]: ==================================================
[2025-06-06 17:03:16][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:03:16][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:03:16][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 17:03:16][MainThread][ERROR]: Traceback (most recent call last):
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 262, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: start_server()
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 234, in start_server
[2025-06-06 17:03:16][MainThread][ERROR]: SpeechRecognitionServicer(), server
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 25, in __init__
[2025-06-06 17:03:16][MainThread][ERROR]: self._init_inference_engine()
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 33, in _init_inference_engine
[2025-06-06 17:03:16][MainThread][ERROR]: self.inference_engine = create_speech_inference(
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference.py", line 169, in create_speech_inference
[2025-06-06 17:03:16][MainThread][ERROR]: return SpeechInference(
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference.py", line 39, in __init__
[2025-06-06 17:03:16][MainThread][ERROR]: self._load_inference_engine()
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference.py", line 54, in _load_inference_engine
[2025-06-06 17:03:16][MainThread][ERROR]: from speech_inference_funasr import SpeechInferenceFunasr
[2025-06-06 17:03:16][MainThread][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_inference_funasr.py", line 8, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: import torchaudio
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/torchaudio/__init__.py", line 1, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: from torchaudio import _extension  # noqa: F401
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/torchaudio/_extension.py", line 5, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: import torch
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/torch/__init__.py", line 29, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: from .torch_version import __version__ as __version__
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/torch/torch_version.py", line 3, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: from pkg_resources import packaging  # type: ignore[attr-defined]
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 3267, in <module>
[2025-06-06 17:03:16][MainThread][ERROR]: def _initialize_master_working_set():
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 3241, in _call_aside
[2025-06-06 17:03:16][MainThread][ERROR]: f(*args, **kwargs)
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 3279, in _initialize_master_working_set
[2025-06-06 17:03:16][MainThread][ERROR]: working_set = WorkingSet._build_master()
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 564, in _build_master
[2025-06-06 17:03:16][MainThread][ERROR]: ws = cls()
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 557, in __init__
[2025-06-06 17:03:16][MainThread][ERROR]: self.add_entry(entry)
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 613, in add_entry
[2025-06-06 17:03:16][MainThread][ERROR]: for dist in find_distributions(entry, True):
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 2077, in find_on_path
[2025-06-06 17:03:16][MainThread][ERROR]: for dist in factory(fullpath):
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 2142, in distributions_from_metadata
[2025-06-06 17:03:16][MainThread][ERROR]: yield Distribution.from_location(
[2025-06-06 17:03:16][MainThread][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/pkg_resources/__init__.py", line 2598, in from_location
[2025-06-06 17:03:16][MainThread][ERROR]: match = EGG_NAME(basename)
[2025-06-06 17:03:16][MainThread][ERROR]: KeyboardInterrupt
[2025-06-06 17:03:18][MainThread][INFO]: ==================================================
[2025-06-06 17:03:18][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:03:18][MainThread][INFO]: ==================================================
[2025-06-06 17:03:18][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:03:18][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:03:18][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 17:03:20][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:03:20][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:03:20][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 17:03:20][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 17:03:25][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 17:03:25][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 17:03:31][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 17:03:31][MainThread][ERROR]: 2025-06-06 17:03:31,453 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 17:03:31][MainThread][INFO]: funasr模型加载成功，耗时: 10.85秒
[2025-06-06 17:03:31][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 17:03:31][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 17:03:31][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:03:31][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:03:31][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:03:31][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:03:31][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:03:31][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:03:31][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:03:31][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:03:31][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:03:48][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 42437392-0e3b-4551-bff7-f75326f70d7f
[2025-06-06 17:03:48][ThreadPoolExecutor-0_0][INFO]: 任务 42437392-0e3b-4551-bff7-f75326f70d7f: 音频数量=1, 语言=zh
[2025-06-06 17:03:48][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:03:48][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.41it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.013', 'extract_feat': '0.185', 'forward': '0.227', 'batch_size': '1', 'rtf': '0.022'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.41it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.41it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.34it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 30.18it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.166', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 30.18it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 30.18it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 28.31it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.011', 'forward': '0.065', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.35it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.22it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.53it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.83it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.253: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.83it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.253: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.82it/s]
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.498秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.495秒 | 清理: 0.000秒
[2025-06-06 17:03:49][ThreadPoolExecutor-0_0][ERROR]: 任务 42437392-0e3b-4551-bff7-f75326f70d7f: 语音识别失败: list indices must be integers or slices, not str
[2025-06-06 17:04:32][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:04:32][MainThread][INFO]: 服务已停止
[2025-06-06 17:04:32][MainThread][INFO]: ==================================================
[2025-06-06 17:04:32][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:04:32][MainThread][INFO]: ==================================================
[2025-06-06 17:04:32][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:04:32][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:04:32][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 17:04:35][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:04:35][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:04:35][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 17:04:35][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 17:04:36][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 17:04:36][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 17:04:42][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 17:04:42][MainThread][ERROR]: 2025-06-06 17:04:42,468 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 17:04:42][MainThread][INFO]: funasr模型加载成功，耗时: 6.78秒
[2025-06-06 17:04:42][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 17:04:42][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 17:04:42][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:04:42][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:04:42][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:04:42][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:04:42][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:04:42][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:04:42][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:04:42][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:04:42][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:04:53][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 9b187ce0-0868-4c37-8b17-842b7c41c381
[2025-06-06 17:04:53][ThreadPoolExecutor-0_0][INFO]: 任务 9b187ce0-0868-4c37-8b17-842b7c41c381: 音频数量=1, 语言=zh
[2025-06-06 17:04:53][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:04:53][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.37it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.015', 'extract_feat': '0.187', 'forward': '0.229', 'batch_size': '1', 'rtf': '0.022'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.37it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.37it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.30it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 30.15it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.035', 'forward': '0.166', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 30.15it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 30.15it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 28.42it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.011', 'forward': '0.067', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.89it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.77it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.98it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.80it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.255: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.80it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.255: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.79it/s]
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.502秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.499秒 | 清理: 0.000秒
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: --- Logging error ---
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Traceback (most recent call last):
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/handlers.py", line 69, in emit
    if self.shouldRollover(record):
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/handlers.py", line 185, in shouldRollover
    msg = "%s\n" % self.format(record)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 929, in format
    return fmt.format(record)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 668, in format
    record.message = record.getMessage()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 373, in getMessage
    msg = msg % self.args
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: TypeError: not all arguments converted during string formatting
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Call stack:
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 80, in _worker
    work_item.run()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 793, in _unary_response_in_pool
    response, proceed = _call_behavior(
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 610, in _call_behavior
    response_or_iterator = behavior(argument, context)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 98, in RecognizeSpeech
    self.logger.info('推理的文本结果为：', inference_results)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Message: '推理的文本结果为：'
Arguments: ([{'key': 'vad_example.wav', 'raw_text': '<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实', 'clean_text': '但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实', 'text': '试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊', 'confidence': 0.0, 'duration': 70.470625, 'language': 'zh'}],)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: --- Logging error ---
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Traceback (most recent call last):
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 1085, in emit
    msg = self.format(record)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 929, in format
    return fmt.format(record)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 668, in format
    record.message = record.getMessage()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 373, in getMessage
    msg = msg % self.args
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: TypeError: not all arguments converted during string formatting
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Call stack:
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 80, in _worker
    work_item.run()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 793, in _unary_response_in_pool
    response, proceed = _call_behavior(
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 610, in _call_behavior
    response_or_iterator = behavior(argument, context)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 98, in RecognizeSpeech
    self.logger.info('推理的文本结果为：', inference_results)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Message: '推理的文本结果为：'
Arguments: ([{'key': 'vad_example.wav', 'raw_text': '<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实', 'clean_text': '但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实', 'text': '试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊', 'confidence': 0.0, 'duration': 70.470625, 'language': 'zh'}],)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: --- Logging error ---
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Traceback (most recent call last):
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 1085, in emit
    msg = self.format(record)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 929, in format
    return fmt.format(record)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 668, in format
    record.message = record.getMessage()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/logging/__init__.py", line 373, in getMessage
    msg = msg % self.args
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: TypeError: not all arguments converted during string formatting
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Call stack:
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 932, in _bootstrap_inner
    self.run()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 80, in _worker
    work_item.run()
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/concurrent/futures/thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 793, in _unary_response_in_pool
    response, proceed = _call_behavior(
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/home/<USER>/anaconda3/envs/yolov5/lib/python3.8/site-packages/grpc/_server.py", line 610, in _call_behavior
    response_or_iterator = behavior(argument, context)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: File "/media/lwq/dd1/share_directory/SenseVoice_new/speech_grpc_server.py", line 98, in RecognizeSpeech
    self.logger.info('推理的文本结果为：', inference_results)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][ERROR]: Message: '推理的文本结果为：'
Arguments: ([{'key': 'vad_example.wav', 'raw_text': '<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实', 'clean_text': '但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实', 'text': '试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊', 'confidence': 0.0, 'duration': 70.470625, 'language': 'zh'}],)
[2025-06-06 17:04:54][ThreadPoolExecutor-0_0][INFO]: 任务 9b187ce0-0868-4c37-8b17-842b7c41c381 完成，处理时间: 0.51秒，结果数量: 1 

[2025-06-06 17:05:23][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:05:23][MainThread][INFO]: 服务已停止
[2025-06-06 17:08:42][MainThread][INFO]: ==================================================
[2025-06-06 17:08:42][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:08:42][MainThread][INFO]: ==================================================
[2025-06-06 17:08:42][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:08:42][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:08:42][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 17:08:45][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:08:45][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:08:45][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 17:08:45][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 17:08:46][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 17:08:46][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 17:08:51][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 17:08:52][MainThread][ERROR]: 2025-06-06 17:08:52,197 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 17:08:52][MainThread][INFO]: funasr模型加载成功，耗时: 7.18秒
[2025-06-06 17:08:52][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 17:08:52][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 17:08:52][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:08:52][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:08:52][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:08:52][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:08:52][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:08:52][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:08:52][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:08:52][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:08:52][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:09:11][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f15269cb-f791-4430-a5e0-aa99998a0795
[2025-06-06 17:09:11][ThreadPoolExecutor-0_0][INFO]: 任务 f15269cb-f791-4430-a5e0-aa99998a0795: 音频数量=1, 语言=zh
[2025-06-06 17:09:11][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:09:11][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.30it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.016', 'extract_feat': '0.191', 'forward': '0.233', 'batch_size': '1', 'rtf': '0.022'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.30it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.30it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.22it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.67it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.195', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.67it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.67it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 24.50it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.009', 'forward': '0.061', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.30it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.16it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.36it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.51it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.277: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.51it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.277: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.50it/s]
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.527秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.525秒 | 清理: 0.000秒
[2025-06-06 17:09:12][ThreadPoolExecutor-0_0][INFO]: 任务 f15269cb-f791-4430-a5e0-aa99998a0795 完成，处理时间: 0.53秒，结果数量: 1 

[2025-06-06 17:10:10][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:10:10][MainThread][INFO]: 服务已停止
[2025-06-06 17:10:22][MainThread][INFO]: ==================================================
[2025-06-06 17:10:22][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:10:22][MainThread][INFO]: ==================================================
[2025-06-06 17:10:22][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:10:22][MainThread][INFO]: 推理模式: local
[2025-06-06 17:10:22][MainThread][INFO]: 使用LOCAL模式 - 本地SenseVoiceSmall模型
[2025-06-06 17:10:26][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:10:26][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:10:26][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 17:10:31][MainThread][INFO]: 模型加载成功，耗时: 4.97秒
[2025-06-06 17:10:31][MainThread][INFO]: 推理引擎加载成功，模式: local
[2025-06-06 17:10:31][MainThread][INFO]: 推理引擎信息: 本地SenseVoiceSmall模型 - 速度快，无需联网，但断句效果一般
[2025-06-06 17:10:31][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:10:31][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:10:31][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:10:31][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:10:31][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:10:31][MainThread][INFO]: 推理模式: local
[2025-06-06 17:10:31][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:10:31][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:10:31][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:10:40][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1bd9b024-eb55-4883-8b00-57adee9be5b1
[2025-06-06 17:10:40][ThreadPoolExecutor-0_0][INFO]: 任务 1bd9b024-eb55-4883-8b00-57adee9be5b1: 音频数量=1, 语言=zh
[2025-06-06 17:10:40][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:10:40][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.311秒 | 验证: 0.000秒 | 音频加载: 0.010秒 | 模型推理: 0.300秒 | 结果处理: 0.001秒
[2025-06-06 17:10:40][ThreadPoolExecutor-0_0][INFO]: 任务 1bd9b024-eb55-4883-8b00-57adee9be5b1 完成，处理时间: 0.31秒，结果数量: 1 

[2025-06-06 17:16:18][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:16:18][MainThread][INFO]: 服务已停止
[2025-06-06 17:16:19][MainThread][INFO]: ==================================================
[2025-06-06 17:16:19][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:16:19][MainThread][INFO]: ==================================================
[2025-06-06 17:16:19][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:16:19][MainThread][INFO]: 推理模式: local
[2025-06-06 17:16:19][MainThread][INFO]: 使用LOCAL模式 - 本地SenseVoiceSmall模型
[2025-06-06 17:16:21][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:16:21][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:16:21][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 17:16:26][MainThread][INFO]: 模型加载成功，耗时: 4.89秒
[2025-06-06 17:16:26][MainThread][INFO]: 推理引擎加载成功，模式: local
[2025-06-06 17:16:26][MainThread][INFO]: 推理引擎信息: 本地SenseVoiceSmall模型 - 速度快，无需联网，但断句效果一般
[2025-06-06 17:16:26][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:16:26][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:16:26][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:16:26][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:16:26][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:16:26][MainThread][INFO]: 推理模式: local
[2025-06-06 17:16:26][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:16:26][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:16:26][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:16:29][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 8fd1aec7-7c50-402d-b6f4-ba547c941416
[2025-06-06 17:16:29][ThreadPoolExecutor-0_0][INFO]: 任务 8fd1aec7-7c50-402d-b6f4-ba547c941416: 音频数量=1, 语言=zh
[2025-06-06 17:16:29][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:16:29][ThreadPoolExecutor-0_0][INFO]: 模型推理的结果为：<|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名唱学卡的同学你们可以听到后面的有专门的活动课他会大哒降低你的试错成本其实你也可以不来听课为什么你自己写嘛我先今天写五个点我就是试验一下反正这五个点不行我再写五个点这是在不行那再写五个点嘛你总会所谓的活动大神和所谓的高手都是只有一个把所有的错所有的坑全过趟一遍留下正确的你就是所谓的大神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有学卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题啊如果真的要坐下来聊的话要聊一天但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-06 17:16:29][ThreadPoolExecutor-0_0][INFO]: 语音识别完成 - 总耗时: 0.272秒 | 验证: 0.000秒 | 音频加载: 0.010秒 | 模型推理: 0.261秒 | 结果处理: 0.001秒
[2025-06-06 17:16:29][ThreadPoolExecutor-0_0][INFO]: 任务 8fd1aec7-7c50-402d-b6f4-ba547c941416 完成，处理时间: 0.27秒，结果数量: 1 

[2025-06-06 17:16:42][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:16:42][MainThread][INFO]: 服务已停止
[2025-06-06 17:17:04][MainThread][INFO]: ==================================================
[2025-06-06 17:17:04][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:17:04][MainThread][INFO]: ==================================================
[2025-06-06 17:17:04][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:17:04][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:17:04][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 17:17:07][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:17:07][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:17:07][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 17:17:07][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 17:17:08][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 17:17:08][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 17:17:14][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 17:17:14][MainThread][ERROR]: 2025-06-06 17:17:14,775 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 17:17:14][MainThread][INFO]: funasr模型加载成功，耗时: 7.95秒
[2025-06-06 17:17:14][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 17:17:14][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 17:17:14][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:17:14][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:17:14][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:17:14][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:17:14][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:17:14][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:17:14][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:17:14][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:17:14][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 4cf5374f-46a8-45ef-a477-8323f51c2896
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][INFO]: 任务 4cf5374f-46a8-45ef-a477-8323f51c2896: 音频数量=1, 语言=zh
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.31it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.015', 'extract_feat': '0.191', 'forward': '0.232', 'batch_size': '1', 'rtf': '0.022'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.31it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.31it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.022: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.23it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.42it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.039', 'forward': '0.197', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.42it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.42it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 24.06it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.010', 'forward': '0.062', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.11it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.99it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.91it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.43it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.283: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.43it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.283: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.42it/s]
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][INFO]: 模型推理的结果为：试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.534秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.531秒 | 清理: 0.000秒
[2025-06-06 17:17:17][ThreadPoolExecutor-0_0][INFO]: 任务 4cf5374f-46a8-45ef-a477-8323f51c2896 完成，处理时间: 0.54秒，结果数量: 1 

[2025-06-06 17:17:28][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:17:28][MainThread][INFO]: 服务已停止
[2025-06-06 17:18:17][MainThread][INFO]: ==================================================
[2025-06-06 17:18:17][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 17:18:17][MainThread][INFO]: ==================================================
[2025-06-06 17:18:17][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 17:18:17][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:18:17][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-06 17:18:20][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 17:18:20][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 17:18:20][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-06 17:18:20][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-06 17:18:37][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-06 17:18:37][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-06 17:18:43][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-06 17:18:43][MainThread][ERROR]: 2025-06-06 17:18:43,992 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-06 17:18:44][MainThread][INFO]: funasr模型加载成功，耗时: 23.51秒
[2025-06-06 17:18:44][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-06 17:18:44][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-06 17:18:44][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 17:18:44][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 17:18:44][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 17:18:44][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 17:18:44][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 17:18:44][MainThread][INFO]: 推理模式: funasr
[2025-06-06 17:18:44][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 17:18:44][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 17:18:44][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 76db82ee-d3ce-4cf7-a76c-12e6808e5773
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][INFO]: 任务 76db82ee-d3ce-4cf7-a76c-12e6808e5773: 音频数量=1, 语言=zh
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.53it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.013', 'extract_feat': '0.180', 'forward': '0.221', 'batch_size': '1', 'rtf': '0.021'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.53it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.021: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.53it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.021: 100%|[34m##########[0m| 1/1 [00:00<00:00,  4.45it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.16it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.037', 'forward': '0.184', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.16it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.16it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.02it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.014', 'forward': '0.066', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.09it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.98it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.30it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.56it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.272: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.56it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.272: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.56it/s]
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][INFO]: 模型推理的结果为：试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.511秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.509秒 | 清理: 0.000秒
[2025-06-06 17:18:47][ThreadPoolExecutor-0_0][INFO]: 任务 76db82ee-d3ce-4cf7-a76c-12e6808e5773 完成，处理时间: 0.51秒，结果数量: 1 

[2025-06-06 17:19:13][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 17:19:13][MainThread][INFO]: 服务已停止
