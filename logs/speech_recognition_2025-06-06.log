[2025-06-06 15:02:52][MainThread][INFO]: ==================================================
[2025-06-06 15:02:52][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:02:52][MainThread][INFO]: ==================================================
[2025-06-06 15:02:52][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:02:52][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:02:52][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:02:52][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:02:56][MainThread][INFO]: 模型加载成功，耗时: 4.83秒
[2025-06-06 15:02:56][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:02:56][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:02:56][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:02:56][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:02:56][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:02:56][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:02:56][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:02:56][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c2e469e8-b792-459d-be1f-daa6a64deda9
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 任务 c2e469e8-b792-459d-be1f-daa6a64deda9: 音频数量=1, 语言=zh
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.13秒，推理耗时: 0.12秒
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 任务 c2e469e8-b792-459d-be1f-daa6a64deda9 完成，处理时间: 0.13秒，结果数量: 1
[2025-06-06 15:04:05][MainThread][INFO]: ==================================================
[2025-06-06 15:04:05][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:04:05][MainThread][INFO]: ==================================================
[2025-06-06 15:04:05][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:04:05][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:04:05][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:04:05][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:04:10][MainThread][INFO]: 模型加载成功，耗时: 4.95秒
[2025-06-06 15:04:10][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:04:10][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:04:10][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:04:10][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:04:10][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:04:10][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:04:10][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:04:10][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: b5e5ab37-8377-4795-97b7-5ba53befeb6d
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 任务 b5e5ab37-8377-4795-97b7-5ba53befeb6d: 音频数量=1, 语言=zh
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.12秒，推理耗时: 0.11秒
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 任务 b5e5ab37-8377-4795-97b7-5ba53befeb6d 完成，处理时间: 0.12秒，结果数量: 1
[2025-06-06 15:09:40][MainThread][INFO]: ==================================================
[2025-06-06 15:09:40][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:09:40][MainThread][INFO]: ==================================================
[2025-06-06 15:09:40][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:09:40][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:09:40][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:09:40][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:09:45][MainThread][INFO]: 模型加载成功，耗时: 5.04秒
[2025-06-06 15:09:45][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:09:45][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:09:45][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:09:45][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:09:45][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:09:45][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:09:45][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:09:45][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 任务 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6: 音频数量=1, 语言=zh
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.10秒，推理耗时: 0.10秒
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 任务 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 15:12:01][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:12:01][MainThread][INFO]: 服务已停止
[2025-06-06 15:15:08][MainThread][INFO]: ==================================================
[2025-06-06 15:15:08][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:15:08][MainThread][INFO]: ==================================================
[2025-06-06 15:15:08][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:15:08][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:15:08][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:15:08][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:15:13][MainThread][INFO]: 模型加载成功，耗时: 5.04秒
[2025-06-06 15:15:13][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:15:13][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:15:13][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:15:13][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:15:13][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:15:13][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:15:13][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:15:13][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ad645245-383c-45c3-a900-ec1ad11ca72b
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 任务 ad645245-383c-45c3-a900-ec1ad11ca72b: 音频数量=1, 语言=zh
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.10秒，推理耗时: 0.10秒
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 任务 ad645245-383c-45c3-a900-ec1ad11ca72b 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 589d505d-c1da-48db-bd5f-6d083f80c839
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 任务 589d505d-c1da-48db-bd5f-6d083f80c839: 音频数量=1, 语言=zh
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 任务 589d505d-c1da-48db-bd5f-6d083f80c839 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 任务 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe: 音频数量=1, 语言=zh
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 任务 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 596b12bb-d691-42c6-a6e0-83b18cb50b32
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 任务 596b12bb-d691-42c6-a6e0-83b18cb50b32: 音频数量=1, 语言=zh
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.30秒
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 任务 596b12bb-d691-42c6-a6e0-83b18cb50b32 完成，处理时间: 0.32秒，结果数量: 1
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c3786c44-e686-4630-9178-18ff1203f827
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 任务 c3786c44-e686-4630-9178-18ff1203f827: 音频数量=1, 语言=zh
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 处理音频 test.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 任务 c3786c44-e686-4630-9178-18ff1203f827 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 2bbef460-e131-4438-b64e-e1d92637b51d
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 任务 2bbef460-e131-4438-b64e-e1d92637b51d: 音频数量=1, 语言=zh
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 处理音频 test.wav 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 任务 2bbef460-e131-4438-b64e-e1d92637b51d 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1391a843-1f81-49b5-b21f-6b519e8afe16
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][INFO]: 任务 1391a843-1f81-49b5-b21f-6b519e8afe16: 音频数量=1, 语言=zh
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:25:08][ThreadPoolExecutor-0_0][INFO]: 任务 1391a843-1f81-49b5-b21f-6b519e8afe16 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:26:12][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:26:12][MainThread][INFO]: 服务已停止
[2025-06-06 15:26:41][MainThread][INFO]: ==================================================
[2025-06-06 15:26:41][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:26:41][MainThread][INFO]: ==================================================
[2025-06-06 15:26:41][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:26:41][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:26:41][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:26:41][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:26:46][MainThread][INFO]: 模型加载成功，耗时: 5.27秒
[2025-06-06 15:26:46][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:26:46][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:26:46][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:26:46][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:26:46][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:26:46][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:26:46][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:26:46][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:26:50][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0b200336-09cf-42fc-b7d5-9dfa06db8d5a
[2025-06-06 15:26:50][ThreadPoolExecutor-0_0][INFO]: 任务 0b200336-09cf-42fc-b7d5-9dfa06db8d5a: 音频数量=1, 语言=zh
[2025-06-06 15:26:53][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:26:53][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.39秒，推理耗时: 0.14秒
[2025-06-06 15:26:53][ThreadPoolExecutor-0_0][INFO]: 任务 0b200336-09cf-42fc-b7d5-9dfa06db8d5a 完成，处理时间: 3.39秒，结果数量: 1
[2025-06-06 15:27:10][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: d9006239-7f04-4e18-b2e4-5e9cb564dae9
[2025-06-06 15:27:10][ThreadPoolExecutor-0_0][INFO]: 任务 d9006239-7f04-4e18-b2e4-5e9cb564dae9: 音频数量=1, 语言=zh
[2025-06-06 15:27:13][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:27:13][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.30秒，推理耗时: 0.11秒
[2025-06-06 15:27:13][ThreadPoolExecutor-0_0][INFO]: 任务 d9006239-7f04-4e18-b2e4-5e9cb564dae9 完成，处理时间: 3.30秒，结果数量: 1
[2025-06-06 15:28:03][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ea4583ee-6388-4730-83ab-e897966b1083
[2025-06-06 15:28:03][ThreadPoolExecutor-0_0][INFO]: 任务 ea4583ee-6388-4730-83ab-e897966b1083: 音频数量=1, 语言=zh
[2025-06-06 15:28:03][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:28:04][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.10秒
[2025-06-06 15:28:04][ThreadPoolExecutor-0_0][INFO]: 任务 ea4583ee-6388-4730-83ab-e897966b1083 完成，处理时间: 0.31秒，结果数量: 1
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 011cda53-5fa3-4920-ad4b-fe077264453c
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 任务 011cda53-5fa3-4920-ad4b-fe077264453c: 音频数量=2, 语言=zh
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.32秒，推理耗时: 0.13秒
[2025-06-06 15:29:38][ThreadPoolExecutor-0_0][INFO]: 任务 011cda53-5fa3-4920-ad4b-fe077264453c 完成，处理时间: 0.32秒，结果数量: 2
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f2b009b1-e53a-4bcb-8694-20dae91cbd86
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 任务 f2b009b1-e53a-4bcb-8694-20dae91cbd86: 音频数量=2, 语言=zh
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 2, 语言: zh
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.11秒
[2025-06-06 15:29:45][ThreadPoolExecutor-0_0][INFO]: 任务 f2b009b1-e53a-4bcb-8694-20dae91cbd86 完成，处理时间: 0.31秒，结果数量: 2
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: d3199888-c197-4d83-bdeb-99422514e4e5
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 d3199888-c197-4d83-bdeb-99422514e4e5: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.33秒，推理耗时: 0.10秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 d3199888-c197-4d83-bdeb-99422514e4e5 完成，处理时间: 0.33秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 65bdc7d3-2e17-49a7-9bc9-65fad6fa9053
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 65bdc7d3-2e17-49a7-9bc9-65fad6fa9053: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.08秒，推理耗时: 0.07秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 65bdc7d3-2e17-49a7-9bc9-65fad6fa9053 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 75160baa-0440-4497-bf90-21594bc2dfb1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 75160baa-0440-4497-bf90-21594bc2dfb1: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.08秒，推理耗时: 0.08秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 75160baa-0440-4497-bf90-21594bc2dfb1 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: a68e35ee-596a-462a-8f8a-bd575b7f75db
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 a68e35ee-596a-462a-8f8a-bd575b7f75db: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.08秒，推理耗时: 0.07秒
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 a68e35ee-596a-462a-8f8a-bd575b7f75db 完成，处理时间: 0.08秒，结果数量: 1
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 30517b3c-f316-490a-b846-fe12ca1bf107
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 任务 30517b3c-f316-490a-b846-fe12ca1bf107: 音频数量=1, 语言=auto
[2025-06-06 15:30:41][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: auto
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.30秒
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 任务 30517b3c-f316-490a-b846-fe12ca1bf107 完成，处理时间: 0.32秒，结果数量: 1
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 899b42b0-e388-452b-a890-efc8c443fd0f
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 任务 899b42b0-e388-452b-a890-efc8c443fd0f: 音频数量=5, 语言=auto
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 asr_example_zh.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 BAC009S0764W0179.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 BAC009S0916W0481.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][WARNING]: 音频 vad_example.wav 采样率不一致: 16000 vs 48000
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 5, 语言: auto
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.56秒，推理耗时: 0.34秒
[2025-06-06 15:30:42][ThreadPoolExecutor-0_0][INFO]: 任务 899b42b0-e388-452b-a890-efc8c443fd0f 完成，处理时间: 0.56秒，结果数量: 5
[2025-06-06 15:35:58][MainThread][INFO]: ==================================================
[2025-06-06 15:35:58][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:35:58][MainThread][INFO]: ==================================================
[2025-06-06 15:35:58][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:35:58][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:35:58][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:35:58][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:36:03][MainThread][INFO]: 模型加载成功，耗时: 5.09秒
[2025-06-06 15:36:03][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:36:03][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:36:03][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:36:03][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:36:03][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:36:03][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:36:03][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:36:03][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:36:12][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 704934a5-5b09-42e1-8967-fbc820c2c484
[2025-06-06 15:36:12][ThreadPoolExecutor-0_0][INFO]: 任务 704934a5-5b09-42e1-8967-fbc820c2c484: 音频数量=1, 语言=zh
[2025-06-06 15:36:15][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:36:15][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 3.31秒，推理耗时: 0.13秒
[2025-06-06 15:36:15][ThreadPoolExecutor-0_0][INFO]: 任务 704934a5-5b09-42e1-8967-fbc820c2c484 完成，处理时间: 3.32秒，结果数量: 1
[2025-06-06 15:37:41][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:37:41][MainThread][INFO]: 服务已停止
[2025-06-06 15:37:45][MainThread][INFO]: ==================================================
[2025-06-06 15:37:45][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:37:45][MainThread][INFO]: ==================================================
[2025-06-06 15:37:45][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:37:45][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:37:45][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:37:45][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:37:50][MainThread][INFO]: 模型加载成功，耗时: 5.09秒
[2025-06-06 15:37:50][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:37:50][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:37:50][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:37:50][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:37:50][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:37:50][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:37:50][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:37:50][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f545ff5e-f467-46f5-bc4e-eed66f8a5ce4
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 任务 f545ff5e-f467-46f5-bc4e-eed66f8a5ce4: 音频数量=1, 语言=zh
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.33秒，推理耗时: 0.14秒
[2025-06-06 15:37:59][ThreadPoolExecutor-0_0][INFO]: 任务 f545ff5e-f467-46f5-bc4e-eed66f8a5ce4 完成，处理时间: 0.33秒，结果数量: 1
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ee1fdfc3-9302-4f28-a309-1a4a35902038
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 任务 ee1fdfc3-9302-4f28-a309-1a4a35902038: 音频数量=1, 语言=zh
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.25秒，推理耗时: 0.09秒
[2025-06-06 15:38:51][ThreadPoolExecutor-0_0][INFO]: 任务 ee1fdfc3-9302-4f28-a309-1a4a35902038 完成，处理时间: 0.26秒，结果数量: 1
