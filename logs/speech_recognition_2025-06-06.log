[2025-06-06 15:02:52][MainThread][INFO]: ==================================================
[2025-06-06 15:02:52][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:02:52][MainThread][INFO]: ==================================================
[2025-06-06 15:02:52][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:02:52][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:02:52][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:02:52][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:02:56][MainThread][INFO]: 模型加载成功，耗时: 4.83秒
[2025-06-06 15:02:56][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:02:56][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:02:56][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:02:56][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:02:56][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:02:56][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:02:56][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:02:56][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c2e469e8-b792-459d-be1f-daa6a64deda9
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 任务 c2e469e8-b792-459d-be1f-daa6a64deda9: 音频数量=1, 语言=zh
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.13秒，推理耗时: 0.12秒
[2025-06-06 15:03:38][ThreadPoolExecutor-0_0][INFO]: 任务 c2e469e8-b792-459d-be1f-daa6a64deda9 完成，处理时间: 0.13秒，结果数量: 1
[2025-06-06 15:04:05][MainThread][INFO]: ==================================================
[2025-06-06 15:04:05][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:04:05][MainThread][INFO]: ==================================================
[2025-06-06 15:04:05][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:04:05][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:04:05][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:04:05][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:04:10][MainThread][INFO]: 模型加载成功，耗时: 4.95秒
[2025-06-06 15:04:10][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:04:10][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:04:10][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:04:10][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:04:10][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:04:10][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:04:10][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:04:10][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: b5e5ab37-8377-4795-97b7-5ba53befeb6d
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 任务 b5e5ab37-8377-4795-97b7-5ba53befeb6d: 音频数量=1, 语言=zh
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.12秒，推理耗时: 0.11秒
[2025-06-06 15:04:44][ThreadPoolExecutor-0_0][INFO]: 任务 b5e5ab37-8377-4795-97b7-5ba53befeb6d 完成，处理时间: 0.12秒，结果数量: 1
[2025-06-06 15:09:40][MainThread][INFO]: ==================================================
[2025-06-06 15:09:40][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:09:40][MainThread][INFO]: ==================================================
[2025-06-06 15:09:40][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:09:40][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:09:40][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:09:40][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:09:45][MainThread][INFO]: 模型加载成功，耗时: 5.04秒
[2025-06-06 15:09:45][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:09:45][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:09:45][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:09:45][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:09:45][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:09:45][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:09:45][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:09:45][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 任务 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6: 音频数量=1, 语言=zh
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.10秒，推理耗时: 0.10秒
[2025-06-06 15:09:51][ThreadPoolExecutor-0_0][INFO]: 任务 8f5fe07f-a8b7-49a8-81e0-591b3a4c92d6 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 15:12:01][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-06 15:12:01][MainThread][INFO]: 服务已停止
[2025-06-06 15:15:08][MainThread][INFO]: ==================================================
[2025-06-06 15:15:08][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-06 15:15:08][MainThread][INFO]: ==================================================
[2025-06-06 15:15:08][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-06 15:15:08][MainThread][INFO]: 开始加载语音识别模型，模型路径: ./SenseVoice_model
[2025-06-06 15:15:08][MainThread][INFO]: 使用设备: cuda:0
[2025-06-06 15:15:08][MainThread][INFO]: Loading remote code successfully: model
[2025-06-06 15:15:13][MainThread][INFO]: 模型加载成功，耗时: 5.04秒
[2025-06-06 15:15:13][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-06 15:15:13][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-06 15:15:13][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-06 15:15:13][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-06 15:15:13][MainThread][INFO]: 推理设备: cuda:0
[2025-06-06 15:15:13][MainThread][INFO]: 最大工作线程: 20
[2025-06-06 15:15:13][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-06 15:15:13][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: ad645245-383c-45c3-a900-ec1ad11ca72b
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 任务 ad645245-383c-45c3-a900-ec1ad11ca72b: 音频数量=1, 语言=zh
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.10秒，推理耗时: 0.10秒
[2025-06-06 15:15:24][ThreadPoolExecutor-0_0][INFO]: 任务 ad645245-383c-45c3-a900-ec1ad11ca72b 完成，处理时间: 0.10秒，结果数量: 1
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 589d505d-c1da-48db-bd5f-6d083f80c839
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 任务 589d505d-c1da-48db-bd5f-6d083f80c839: 音频数量=1, 语言=zh
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:15:54][ThreadPoolExecutor-0_0][INFO]: 任务 589d505d-c1da-48db-bd5f-6d083f80c839 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 任务 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe: 音频数量=1, 语言=zh
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 处理音频 语音识别.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:16:28][ThreadPoolExecutor-0_0][INFO]: 任务 78d3fb64-90de-480e-b6e0-72b6f7e1ebbe 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 596b12bb-d691-42c6-a6e0-83b18cb50b32
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 任务 596b12bb-d691-42c6-a6e0-83b18cb50b32: 音频数量=1, 语言=zh
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 开始语音识别推理，音频数量: 1, 语言: zh
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 语音识别完成，总耗时: 0.31秒，推理耗时: 0.30秒
[2025-06-06 15:17:30][ThreadPoolExecutor-0_0][INFO]: 任务 596b12bb-d691-42c6-a6e0-83b18cb50b32 完成，处理时间: 0.32秒，结果数量: 1
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: c3786c44-e686-4630-9178-18ff1203f827
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 任务 c3786c44-e686-4630-9178-18ff1203f827: 音频数量=1, 语言=zh
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 处理音频 test.mp3 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:18:03][ThreadPoolExecutor-0_0][INFO]: 任务 c3786c44-e686-4630-9178-18ff1203f827 完成，处理时间: 0.00秒，结果数量: 0
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 2bbef460-e131-4438-b64e-e1d92637b51d
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 任务 2bbef460-e131-4438-b64e-e1d92637b51d: 音频数量=1, 语言=zh
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 音频加载失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 处理音频 test.wav 失败: Error loading audio file: failed to open file <in memory buffer>
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][ERROR]: 没有成功加载的音频文件
[2025-06-06 15:19:34][ThreadPoolExecutor-0_0][INFO]: 任务 2bbef460-e131-4438-b64e-e1d92637b51d 完成，处理时间: 0.00秒，结果数量: 0
