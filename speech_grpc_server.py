import os
import sys
import time
import grpc
import logging
import threading
from concurrent import futures
from typing import List, Dict, Any

# 导入生成的gRPC代码
import speech_recognition_pb2
import speech_recognition_pb2_grpc

# 导入自定义模块
from speech_grpc_config import config
from speech_logger import setup_speech_logger
from speech_inference import SpeechInference


class SpeechRecognitionServicer(speech_recognition_pb2_grpc.SpeechRecognitionServiceServicer):
    """语音识别gRPC服务实现"""
    
    def __init__(self):
        self.logger = logging.getLogger("speech_recognition")
        self.inference_engine = None
        self._init_inference_engine()
    
    def _init_inference_engine(self):
        """初始化推理引擎"""
        try:
            self.logger.info("初始化语音识别推理引擎...")
            self.inference_engine = SpeechInference(
                model_dir=config.model_dir,
                device=config.device
            )
            self.logger.info("语音识别推理引擎初始化成功")
        except Exception as e:
            self.logger.error(f"推理引擎初始化失败: {e}")
            sys.exit(1)
    
    def RecognizeSpeech(self, request, context):
        """语音识别接口实现"""
        start_time = time.time()
        task_id = request.taskId
        
        self.logger.info(f"收到语音识别请求，任务ID: {task_id}")
        
        try:
            # 验证请求
            if not request.audioFiles:
                error_msg = "音频文件列表为空"
                self.logger.error(f"任务 {task_id}: {error_msg}")
                return speech_recognition_pb2.SpeechRecognitionReply(
                    code=400,
                    message=error_msg,
                    taskId=task_id,
                    results=[],
                    processingTime=0.0
                )
            
            # 检查音频文件大小
            total_size = sum(len(audio) for audio in request.audioFiles)
            if total_size > config.max_audio_size:
                error_msg = f"音频文件总大小超过限制: {total_size} > {config.max_audio_size}"
                self.logger.error(f"任务 {task_id}: {error_msg}")
                return speech_recognition_pb2.SpeechRecognitionReply(
                    code=413,
                    message=error_msg,
                    taskId=task_id,
                    results=[],
                    processingTime=0.0
                )
            
            # 准备推理参数
            audio_files = list(request.audioFiles)
            audio_keys = list(request.audioKeys) if request.audioKeys else None
            language = request.language if request.language else "auto"
            use_itn = request.useItn
            ban_emo_unk = request.banEmoUnk
            
            self.logger.info(f"任务 {task_id}: 音频数量={len(audio_files)}, 语言={language}")
            
            # 执行推理
            inference_results = self.inference_engine.inference(
                audio_files=audio_files,
                audio_keys=audio_keys,
                language=language,
                use_itn=use_itn,
                ban_emo_unk=ban_emo_unk
            )
            
            # 构建响应结果
            results = []
            for result in inference_results:
                pb_result = speech_recognition_pb2.RecognitionResult(
                    key=result["key"],
                    rawText=result["raw_text"],
                    cleanText=result["clean_text"],
                    text=result["text"],
                    confidence=result["confidence"],
                    duration=result["duration"],
                    language=result["language"]
                )
                results.append(pb_result)
            
            processing_time = time.time() - start_time
            
            self.logger.info(f"任务 {task_id} 完成，处理时间: {processing_time:.2f}秒，结果数量: {len(results)}")
            
            return speech_recognition_pb2.SpeechRecognitionReply(
                code=200,
                message="识别成功",
                taskId=task_id,
                results=results,
                processingTime=processing_time
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"语音识别失败: {str(e)}"
            self.logger.error(f"任务 {task_id}: {error_msg}")
            
            return speech_recognition_pb2.SpeechRecognitionReply(
                code=500,
                message=error_msg,
                taskId=task_id,
                results=[],
                processingTime=processing_time
            )
    
    def UpdateModel(self, request, context):
        """模型更新接口实现"""
        self.logger.info(f"收到模型更新请求: {request.fileName}")
        
        # 这里可以实现模型更新逻辑
        # 目前返回不支持的响应
        return speech_recognition_pb2.UpdateModelReply(
            code=501,
            message="模型更新功能暂未实现"
        )
    
    def HealthCheck(self, request, context):
        """健康检查接口实现"""
        try:
            if self.inference_engine and self.inference_engine.health_check():
                return speech_recognition_pb2.HealthCheckReply(
                    code=200,
                    message="服务正常",
                    status="SERVING"
                )
            else:
                return speech_recognition_pb2.HealthCheckReply(
                    code=503,
                    message="服务不可用",
                    status="NOT_SERVING"
                )
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return speech_recognition_pb2.HealthCheckReply(
                code=500,
                message=f"健康检查异常: {str(e)}",
                status="NOT_SERVING"
            )


def write_pid(service_name: str):
    """写入进程ID文件"""
    try:
        pid_file = f'/tmp/{service_name}.pid'
        with open(pid_file, 'w') as f:
            f.write(str(os.getpid()))
        logging.info(f"PID文件已写入: {pid_file}")
    except Exception as e:
        logging.error(f"写入PID文件失败: {e}")


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger = logging.getLogger("speech_recognition")
    logger.error('未捕获的异常', exc_info=(exc_type, exc_value, exc_traceback))


def start_server():
    """启动gRPC服务器"""
    # 写入PID文件
    write_pid(config.service_name)
    
    # 初始化日志系统
    logger = setup_speech_logger(
        app_name=config.service_name,
        log_dir=config.log_dir,
        log_level=getattr(logging, config.log_level.upper()),
        days_to_keep=config.log_days_to_keep,
        redirect_stdout=True
    )
    
    # 设置全局异常处理
    sys.excepthook = handle_exception
    
    logger.info("=" * 50)
    logger.info("启动语音识别gRPC服务")
    logger.info("=" * 50)
    
    # 配置gRPC服务器选项
    server_options = [
        ('grpc.max_receive_message_length', config.max_receive_message_length),
        ('grpc.max_send_message_length', config.max_send_message_length),
        ('grpc.keepalive_time_ms', 30000),
        ('grpc.keepalive_timeout_ms', 10000),
        ('grpc.keepalive_permit_without_calls', True),
        ('grpc.http2.max_pings_without_data', 0),
        ('grpc.http2.min_time_between_pings_ms', 10000),
        ('grpc.http2.min_ping_interval_without_data_ms', 300000)
    ]
    
    # 创建gRPC服务器
    server = grpc.server(
        thread_pool=futures.ThreadPoolExecutor(max_workers=config.max_workers),
        options=server_options
    )
    
    # 添加服务
    speech_recognition_pb2_grpc.add_SpeechRecognitionServiceServicer_to_server(
        SpeechRecognitionServicer(), server
    )
    
    # 绑定端口
    listen_addr = f"0.0.0.0:{config.port}"
    server.add_insecure_port(listen_addr)
    
    # 启动服务器
    server.start()
    
    logger.info(f"服务名称: {config.service_name}")
    logger.info(f"监听地址: {listen_addr}")
    logger.info(f"模型路径: {config.model_dir}")
    logger.info(f"推理设备: {config.device}")
    logger.info(f"最大工作线程: {config.max_workers}")
    logger.info(f"最大消息大小: {config.max_receive_message_length / 1024 / 1024:.1f}MB")
    logger.info("语音识别gRPC服务启动成功")
    
    try:
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务...")
        server.stop(grace=10)
        logger.info("服务已停止")


if __name__ == "__main__":
    start_server()
