#!/usr/bin/env python3
"""
警告抑制模块 - 用于抑制各种库产生的不必要警告
"""

import os
import sys
import warnings
import logging


def setup_warning_suppression():
    """设置警告抑制"""
    
    # 设置环境变量来减少gRPC相关警告
    os.environ.setdefault('GRPC_POLL_STRATEGY', 'poll')
    os.environ.setdefault('GRPC_VERBOSITY', 'ERROR')
    os.environ.setdefault('GRPC_TRACE', '')
    
    # 抑制pydub相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="pydub")
    warnings.filterwarnings("ignore", message=".*can't determine type of file.*")
    warnings.filterwarnings("ignore", message=".*Couldn't find ffmpeg or avconv.*")
    warnings.filterwarnings("ignore", message=".*Couldn't find ffprobe or avprobe.*")
    
    # 抑制gRPC fork相关警告
    warnings.filterwarnings("ignore", message=".*fork.*")
    warnings.filterwarnings("ignore", message=".*Other threads are currently calling into gRPC.*")
    
    # 抑制torchaudio相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="torchaudio")
    warnings.filterwarnings("ignore", message=".*torchaudio.*")
    
    # 抑制librosa相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="librosa")
    
    # 抑制torch相关警告
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")
    
    # 设置日志级别来减少不必要的输出
    logging.getLogger("pydub").setLevel(logging.ERROR)
    logging.getLogger("librosa").setLevel(logging.ERROR)
    logging.getLogger("torchaudio").setLevel(logging.ERROR)
    
    # 重定向stderr来捕获C库的警告
    class StderrFilter:
        def __init__(self, original_stderr):
            self.original_stderr = original_stderr
            self.buffer = []
        
        def write(self, message):
            # 过滤掉特定的警告消息
            if any(pattern in message.lower() for pattern in [
                "can't determine type of file",
                "other threads are currently calling into grpc",
                "fork() handlers",
                "ffmpeg",
                "avconv"
            ]):
                return  # 忽略这些消息
            
            # 其他消息正常输出
            self.original_stderr.write(message)
        
        def flush(self):
            self.original_stderr.flush()
        
        def fileno(self):
            return self.original_stderr.fileno()
    
    # 应用stderr过滤器
    # sys.stderr = StderrFilter(sys.stderr)


def suppress_specific_warnings():
    """抑制特定的运行时警告"""
    import contextlib
    
    @contextlib.contextmanager
    def suppress_warnings():
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            yield
    
    return suppress_warnings


# 在模块导入时自动设置警告抑制
setup_warning_suppression()
