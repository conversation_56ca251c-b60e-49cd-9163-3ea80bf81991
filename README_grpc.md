# 语音识别gRPC服务

基于SenseVoice模型的语音识别gRPC服务，支持多种语言的语音转文字功能。

## 功能特性

- 🎯 **多语言支持**: 支持中文、英文、粤语、日语、韩语等多种语言
- 🚀 **高性能**: 基于gRPC协议，支持高并发请求
- 📦 **批量处理**: 支持单次请求处理多个音频文件
- 🔧 **灵活配置**: 支持多种音频格式和参数配置
- 📊 **完整日志**: 详细的日志记录和监控
- 🛡️ **健康检查**: 内置健康检查接口

## 项目结构

```
├── speech_recognition.proto          # gRPC协议定义文件
├── speech_recognition_pb2.py         # 自动生成的protobuf代码
├── speech_recognition_pb2_grpc.py    # 自动生成的gRPC代码
├── speech_grpc_server.py             # gRPC服务器主程序
├── speech_grpc_client.py             # gRPC客户端示例
├── speech_grpc_config.py             # 配置管理模块
├── speech_grpc_config.json           # 配置文件
├── speech_logger.py                  # 日志管理模块
├── speech_inference.py               # 语音识别推理模块
├── start_speech_grpc.sh              # 服务启动脚本
└── README_grpc.md                    # 说明文档
```

## 安装依赖

```bash
# 安装gRPC相关依赖
pip install grpcio grpcio-tools

# 确保已安装其他必要依赖
pip install torch torchaudio funasr
```

## 配置说明

编辑 `speech_grpc_config.json` 文件来配置服务参数：

```json
{
    "service_name": "speech_recognition",
    "port": "50051",
    "max_workers": 20,
    "model_dir": "./SenseVoice_model",
    "device": "cuda:0",
    "default_sample_rate": 16000,
    "supported_formats": [".wav", ".mp3", ".flac", ".m4a"],
    "max_audio_size": 52428800,
    "log_dir": "./logs",
    "log_level": "INFO",
    "log_days_to_keep": 7,
    "max_receive_message_length": 52428800,
    "max_send_message_length": 52428800
}
```

### 环境变量配置

- `SENSEVOICE_DEVICE`: 推理设备 (默认: cuda:0)
- `SPEECH_GRPC_PORT`: 服务端口 (默认: 50051)

## 使用方法

### 1. 启动服务

```bash
# 使用启动脚本（推荐）
./start_speech_grpc.sh start

# 或直接运行Python脚本
python speech_grpc_server.py
```

### 2. 管理服务

```bash
# 查看服务状态
./start_speech_grpc.sh status

# 停止服务
./start_speech_grpc.sh stop

# 重启服务
./start_speech_grpc.sh restart

# 查看实时日志
./start_speech_grpc.sh logs

# 显示帮助
./start_speech_grpc.sh help
```

### 3. 测试服务

```bash
# 运行测试客户端
python speech_grpc_client.py
```

## gRPC接口说明

### 1. 语音识别接口

**方法**: `RecognizeSpeech`

**请求参数**:
- `taskId`: 任务ID
- `audioFiles`: 音频文件字节数据列表
- `audioKeys`: 音频文件名称列表
- `language`: 语言类型 (auto, zh, en, yue, ja, ko, nospeech)
- `useItn`: 是否使用逆文本标准化
- `banEmoUnk`: 是否禁用情感和未知标记
- `sampleRate`: 采样率
- `metadata`: 扩展元数据

**响应结果**:
- `code`: 状态码 (200=成功)
- `message`: 响应消息
- `taskId`: 任务ID
- `results`: 识别结果列表
- `processingTime`: 处理时间

### 2. 健康检查接口

**方法**: `HealthCheck`

**请求参数**:
- `service`: 服务名称

**响应结果**:
- `code`: 状态码
- `message`: 响应消息
- `status`: 服务状态 (SERVING/NOT_SERVING)

### 3. 模型更新接口

**方法**: `UpdateModel`

**请求参数**:
- `fileName`: 模型文件名
- `fileType`: 模型文件类型
- `downloadUrl`: 下载URL

**响应结果**:
- `code`: 状态码
- `message`: 响应消息

## 客户端示例

### Python客户端

```python
from speech_grpc_client import SpeechRecognitionClient

# 创建客户端
client = SpeechRecognitionClient("localhost:50051")

# 执行语音识别
result = client.recognize_speech(
    audio_files=["./voice_test/asr_example_zh.wav"],
    language="zh"
)

print(f"识别结果: {result['results'][0]['text']}")

# 关闭连接
client.close()
```

### 其他语言客户端

可以使用protobuf定义文件生成其他语言的客户端代码：

```bash
# 生成Java代码
protoc --java_out=./java --proto_path=. speech_recognition.proto

# 生成Go代码
protoc --go_out=./go --go-grpc_out=./go --proto_path=. speech_recognition.proto

# 生成C++代码
protoc --cpp_out=./cpp --grpc_out=./cpp --plugin=protoc-gen-grpc=grpc_cpp_plugin --proto_path=. speech_recognition.proto
```

## 性能优化

1. **GPU加速**: 确保CUDA环境正确配置
2. **批量处理**: 单次请求处理多个音频文件
3. **连接池**: 客户端使用连接池复用连接
4. **消息大小**: 根据需要调整最大消息大小限制

## 监控和日志

- 日志文件位置: `./logs/speech_recognition_YYYY-MM-DD.log`
- 日志自动轮转和清理
- 支持实时日志查看
- 详细的性能指标记录

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认CUDA环境配置
   - 查看日志文件获取详细错误信息

2. **端口占用**
   - 修改配置文件中的端口号
   - 或使用环境变量 `SPEECH_GRPC_PORT`

3. **内存不足**
   - 减少 `max_workers` 数量
   - 限制音频文件大小
   - 使用CPU推理模式

4. **音频格式不支持**
   - 确认音频格式在支持列表中
   - 转换音频格式为wav或mp3

## 与原HTTP API的对比

| 特性 | HTTP API | gRPC API |
|------|----------|----------|
| 协议 | HTTP/REST | gRPC/HTTP2 |
| 性能 | 中等 | 高 |
| 类型安全 | 弱 | 强 |
| 流式处理 | 不支持 | 支持 |
| 多语言支持 | 需手动实现 | 自动生成 |
| 负载均衡 | 需额外配置 | 内置支持 |

## 许可证

本项目基于原SenseVoice项目，请遵循相应的开源许可证。
